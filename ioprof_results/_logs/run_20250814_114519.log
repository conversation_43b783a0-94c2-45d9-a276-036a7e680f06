Installing dependencies if needed...
Get:1 http://security.ubuntu.com/ubuntu focal-security InRelease [128 kB]
Hit:2 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal InRelease
Get:3 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates InRelease [128 kB]
Get:4 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-backports InRelease [128 kB]
Fetched 383 kB in 4s (95.6 kB/s)
Reading package lists...
Reading package lists...
Building dependency tree...
Reading state information...
The following packages were automatically installed and are no longer required:
  logrotate ssl-cert
Use 'apt autoremove' to remove them.
The following additional packages will be installed:
  accountsservice aglfn apg apport apport-symptoms aptdaemon aptdaemon-data
  aspell aspell-en avahi-daemon avahi-utils bind9-host bind9-libs bluez bolt
  bubblewrap cheese-common colord colord-data cracklib-runtime crda
  cups-pk-helper dbus-x11 dconf-cli desktop-file-utils dictionaries-common
  distro-info-data dns-root-data dnsmasq-base docbook-xml emacsen-common
  enchant-2 evolution-data-server evolution-data-server-common fprintd gcr
  gdm3 geoclue-2.0 gir1.2-accountsservice-1.0 gir1.2-gck-1 gir1.2-gcr-3
  gir1.2-gdesktopenums-3.0 gir1.2-gdm-1.0 gir1.2-geoclue-2.0
  gir1.2-gnomebluetooth-1.0 gir1.2-gnomedesktop-3.0 gir1.2-graphene-1.0
  gir1.2-gweather-3.0 gir1.2-ibus-1.0 gir1.2-json-1.0 gir1.2-mutter-6
  gir1.2-nm-1.0 gir1.2-nma-1.0 gir1.2-notify-0.7 gir1.2-packagekitglib-1.0
  gir1.2-polkit-1.0 gir1.2-rsvg-2.0 gir1.2-secret-1 gir1.2-soup-2.4
  gir1.2-upowerglib-1.0 gir1.2-vte-2.91 gjs gkbd-capplet gnome-control-center
  gnome-control-center-data gnome-control-center-faces gnome-desktop3-data
  gnome-keyring gnome-keyring-pkcs11 gnome-menus gnome-online-accounts
  gnome-session-bin gnome-session-common gnome-settings-daemon
  gnome-settings-daemon-common gnome-shell gnome-shell-common
  gnome-startup-applications gnome-user-docs gnuplot-data gnuplot-qt
  gstreamer1.0-clutter-3.0 gstreamer1.0-gl gstreamer1.0-pulseaudio
  hunspell-en-us ibus ibus-data ibus-gtk ibus-gtk3 iio-sensor-proxy im-config
  ippusbxd iptables iw keyboard-configuration kmod language-selector-common
  language-selector-gnome libaccountsservice0 libappindicator3-1 libappstream4
  libasound2-plugins libaspell15 libavahi-core7 libbluetooth3 libcamel-1.2-62
  libcanberra-gtk3-0 libcanberra-gtk3-module libcanberra-pulse libcheese-gtk25
  libcheese8 libclutter-1.0-0 libclutter-1.0-common libclutter-gst-3.0-0
  libclutter-gtk-1.0-0 libcogl-common libcogl-pango20 libcogl-path20 libcogl20
  libcolord-gtk1 libcolorhug2 libcrack2 libdaemon0 libdbusmenu-glib4
  libdbusmenu-gtk3-4 libdouble-conversion3 libebackend-1.2-10 libebook-1.2-20
  libebook-contacts-1.2-3 libecal-2.0-1 libedata-book-1.2-26
  libedata-cal-2.0-1 libedataserver-1.2-24 libedataserverui-1.2-2
  libenchant-2-2 libevdev2 libexif12 libfontenc1 libfprint-2-2 libgail-common
  libgail18 libgck-1-0 libgcr-base-3-1 libgcr-ui-3-1 libgd3 libgdata-common
  libgdata22 libgdm1 libgee-0.8-2 libgeoclue-2-0 libgeocode-glib0 libgjs0g
  libgnome-autoar-0-0 libgnome-bluetooth13 libgnome-desktop-3-19
  libgnomekbd-common libgnomekbd8 libgoa-1.0-0b libgoa-1.0-common
  libgoa-backend-1.0-1 libgphoto2-6 libgphoto2-l10n libgphoto2-port12
  libgraphene-1.0-0 libgsound0 libgssdp-1.2-0 libgstreamer-gl1.0-0 libgtk2.0-0
  libgtk2.0-bin libgtk2.0-common libgtop-2.0-11 libgtop2-common libgupnp-1.2-0
  libgupnp-av-1.0-2 libgupnp-dlna-2.0-3 libgusb2 libgweather-3-16
  libgweather-common libhunspell-1.7-0 libhyphen0 libibus-1.0-5 libical3
  libieee1284-3 libimobiledevice6 libinput-bin libinput10 libip6tc2
  libjansson4 libjavascriptcoregtk-4.0-18 libldb2 liblmdb0 liblua5.3-0
  libmaxminddb0 libmbim-glib4 libmbim-proxy libmediaart-2.0-0 libmm-glib0
  libmnl0 libmozjs-68-0 libmtdev1 libmutter-6-0 libmysqlclient21 libndp0
  libnetfilter-conntrack3 libnewt0.52 libnfnetlink0 libnftnl11
  libnl-genl-3-200 libnm0 libnma0 libnotify4 libnss-mdns
  libpackagekit-glib2-18 libpam-fprintd libpam-gnome-keyring libpcap0.8
  libpci3 libphonenumber7 libplist3 libpolkit-agent-1-0 libpolkit-gobject-1-0
  libprotobuf17 libpulse-mainloop-glib0 libpulsedsp libpwquality-common
  libpwquality1 libqmi-glib5 libqmi-proxy libqt5core5a libqt5dbus5 libqt5gui5
  libqt5network5 libqt5printsupport5 libqt5svg5 libqt5widgets5
  librygel-core-2.6-2 librygel-db-2.6-2 librygel-renderer-2.6-2
  librygel-server-2.6-2 libsane libsane-common libsbc1 libsecret-1-0
  libsecret-common libsmbclient libsnapd-glib1 libsnmp-base libsnmp35
  libspeexdsp1 libstartup-notification0 libstemmer0d libtalloc2 libteamdctl0
  libtevent0 libtext-iconv-perl libudisks2-0 libunwind8 libupower-glib3
  libusbmuxd6 libwacom-bin libwacom-common libwacom2 libwbclient0
  libwebkit2gtk-4.0-37 libwebrtc-audio-processing1 libwhoopsie-preferences0
  libwhoopsie0 libwoff1 libwxbase3.0-0v5 libwxgtk3.0-gtk3-0v5 libxatracker2
  libxaw7 libxcb-icccm4 libxcb-image0 libxcb-keysyms1 libxcb-render-util0
  libxcb-res0 libxcb-util1 libxcb-xinerama0 libxcb-xinput0 libxcb-xkb1
  libxcb-xv0 libxfont2 libxkbcommon-x11-0 libxkbfile1 libxklavier16 libxmu6
  libxpm4 libxslt1.1 libxt6 libxtables12 libxvmc1 libyelp0 lsb-release
  mobile-broadband-provider-info modemmanager mousetweaks mutter mutter-common
  mysql-common network-manager network-manager-gnome network-manager-pptp
  p11-kit p11-kit-modules packagekit packagekit-tools pci.ids pinentry-gnome3
  policykit-1 ppp pptp-linux pulseaudio pulseaudio-module-bluetooth
  pulseaudio-utils python-apt-common python3-apport python3-apt
  python3-aptdaemon python3-aptdaemon.gtk3widgets python3-blinker
  python3-cairo python3-certifi python3-cffi-backend python3-chardet
  python3-cryptography python3-cups python3-cupshelpers python3-defer
  python3-distro python3-entrypoints python3-httplib2 python3-ibus-1.0
  python3-idna python3-jwt python3-keyring python3-launchpadlib
  python3-lazr.restfulclient python3-lazr.uri python3-ldb
  python3-macaroonbakery python3-nacl python3-oauthlib python3-problem-report
  python3-protobuf python3-pymacaroons python3-requests
  python3-requests-unixsocket python3-rfc3339 python3-secretstorage
  python3-simplejson python3-systemd python3-talloc python3-tz python3-urllib3
  python3-wadllib qt5-gtk-platformtheme qttranslations5-l10n rtkit rygel
  samba-libs sane-utils session-migration sgml-base sgml-data
  switcheroo-control system-config-printer system-config-printer-common
  system-config-printer-udev ubuntu-docs ubuntu-session ubuntu-wallpapers
  ubuntu-wallpapers-focal udev upower usb-modeswitch usb-modeswitch-data
  usb.ids usbmuxd wamerican whoopsie-preferences wireless-regdb wpasupplicant
  x11-xkb-utils x11-xserver-utils xdg-dbus-proxy xfonts-base xfonts-encodings
  xfonts-utils xml-core xserver-common xserver-xephyr xserver-xorg
  xserver-xorg-core xserver-xorg-input-all xserver-xorg-input-libinput
  xserver-xorg-input-wacom xserver-xorg-legacy xserver-xorg-video-all
  xserver-xorg-video-amdgpu xserver-xorg-video-ati xserver-xorg-video-fbdev
  xserver-xorg-video-intel xserver-xorg-video-nouveau xserver-xorg-video-qxl
  xserver-xorg-video-radeon xserver-xorg-video-vesa xserver-xorg-video-vmware
  xwayland yaru-theme-gnome-shell yelp yelp-xsl zenity zenity-common
Suggested packages:
  apport-gtk | apport-kde aspell-doc spellutils avahi-autoipd
  colord-sensor-argyll docbook docbook-dsssl docbook-xsl docbook-defguide
  evolution gnome-orca gnome-software | gnome-packagekit gnome-user-share
  realmd libcanberra-gtk-module usbguard chrome-gnome-shell
  gir1.2-telepathyglib-0.12 gnome-themes-standard-data gnome-backgrounds
  gir1.2-telepathylogger-0.2 gnuplot-doc hunspell openoffice.org-hunspell
  | openoffice.org-core ibus-clutter ibus-doc firewalld nftables
  indicator-application libenchant-2-voikko libgd-tools gphoto2 gvfs
  libusbmuxd-tools mmdb-bin avahi-autoipd | zeroconf qt5-image-formats-plugins
  qtwayland5 hplip snapd snmp-mibs-downloader gstreamer1.0-libav libteam-utils
  isc-dhcp-client network-manager-openconnect-gnome
  network-manager-openvpn-gnome network-manager-vpnc-gnome
  network-manager-pptp-gnome appstream pinentry-doc pavumeter pavucontrol
  paman paprefs ubuntu-sounds python3-apt-dbg python-apt-doc
  python-blinker-doc python-cryptography-doc python3-cryptography-vectors
  python3-crypto libkf5wallet-bin python3-keyrings.alt python3-testresources
  python-nacl-doc python3-openssl python3-socks python-secretstorage-doc
  gstreamer1.0-plugins-ugly rygel-playbin rygel-preferences rygel-ruih
  rygel-tracker tumbler unpaper sgml-base-doc perlsgml w3-recs opensp
  libxml2-utils gnome-software python3-smbc ubuntu-wallpapers-karmic
  ubuntu-wallpapers-lucid ubuntu-wallpapers-maverick ubuntu-wallpapers-natty
  ubuntu-wallpapers-oneiric ubuntu-wallpapers-precise
  ubuntu-wallpapers-quantal ubuntu-wallpapers-raring ubuntu-wallpapers-saucy
  ubuntu-wallpapers-trusty ubuntu-wallpapers-utopic ubuntu-wallpapers-vivid
  ubuntu-wallpapers-wily ubuntu-wallpapers-xenial ubuntu-wallpapers-yakkety
  ubuntu-wallpapers-zesty ubuntu-wallpapers-artful ubuntu-wallpapers-bionic
  ubuntu-wallpapers-cosmic ubuntu-wallpapers-disco ubuntu-wallpapers-eoan
  comgt wvdial wpagui libengine-pkcs11-openssl nickle cairo-5c xorg-docs-core
  debhelper xfonts-100dpi | xfonts-75dpi xfonts-scalable xinput
  firmware-amd-graphics xserver-xorg-video-r128 xserver-xorg-video-mach64
  firmware-misc-nonfree
The following NEW packages will be installed:
  accountsservice aglfn apg apport apport-symptoms aptdaemon aptdaemon-data
  aspell aspell-en avahi-daemon avahi-utils bind9-host bind9-libs bluez bolt
  bubblewrap cheese-common colord colord-data cracklib-runtime crda
  cups-pk-helper dbus-x11 dconf-cli desktop-file-utils dictionaries-common
  distro-info-data dns-root-data dnsmasq-base docbook-xml emacsen-common
  enchant-2 evolution-data-server evolution-data-server-common fprintd gcr
  gdm3 geoclue-2.0 gir1.2-accountsservice-1.0 gir1.2-gck-1 gir1.2-gcr-3
  gir1.2-gdesktopenums-3.0 gir1.2-gdm-1.0 gir1.2-geoclue-2.0
  gir1.2-gnomebluetooth-1.0 gir1.2-gnomedesktop-3.0 gir1.2-graphene-1.0
  gir1.2-gweather-3.0 gir1.2-ibus-1.0 gir1.2-json-1.0 gir1.2-mutter-6
  gir1.2-nm-1.0 gir1.2-nma-1.0 gir1.2-notify-0.7 gir1.2-packagekitglib-1.0
  gir1.2-polkit-1.0 gir1.2-rsvg-2.0 gir1.2-secret-1 gir1.2-soup-2.4
  gir1.2-upowerglib-1.0 gir1.2-vte-2.91 gjs gkbd-capplet gnome-control-center
  gnome-control-center-data gnome-control-center-faces gnome-desktop3-data
  gnome-keyring gnome-keyring-pkcs11 gnome-menus gnome-online-accounts
  gnome-session-bin gnome-session-common gnome-settings-daemon
  gnome-settings-daemon-common gnome-shell gnome-shell-common
  gnome-startup-applications gnome-user-docs gnuplot gnuplot-data gnuplot-qt
  gstreamer1.0-clutter-3.0 gstreamer1.0-gl gstreamer1.0-pulseaudio
  hunspell-en-us ibus ibus-data ibus-gtk ibus-gtk3 iio-sensor-proxy im-config
  ippusbxd iptables iw keyboard-configuration kmod language-selector-common
  language-selector-gnome libaccountsservice0 libappindicator3-1 libappstream4
  libasound2-plugins libaspell15 libavahi-core7 libbluetooth3 libcamel-1.2-62
  libcanberra-gtk3-0 libcanberra-gtk3-module libcanberra-pulse libcheese-gtk25
  libcheese8 libclutter-1.0-0 libclutter-1.0-common libclutter-gst-3.0-0
  libclutter-gtk-1.0-0 libcogl-common libcogl-pango20 libcogl-path20 libcogl20
  libcolord-gtk1 libcolorhug2 libcrack2 libdaemon0 libdbusmenu-glib4
  libdbusmenu-gtk3-4 libdouble-conversion3 libebackend-1.2-10 libebook-1.2-20
  libebook-contacts-1.2-3 libecal-2.0-1 libedata-book-1.2-26
  libedata-cal-2.0-1 libedataserver-1.2-24 libedataserverui-1.2-2
  libenchant-2-2 libevdev2 libexif12 libfontenc1 libfprint-2-2 libgail-common
  libgail18 libgck-1-0 libgcr-base-3-1 libgcr-ui-3-1 libgd3 libgdata-common
  libgdata22 libgdm1 libgee-0.8-2 libgeoclue-2-0 libgeocode-glib0 libgjs0g
  libgnome-autoar-0-0 libgnome-bluetooth13 libgnome-desktop-3-19
  libgnomekbd-common libgnomekbd8 libgoa-1.0-0b libgoa-1.0-common
  libgoa-backend-1.0-1 libgphoto2-6 libgphoto2-l10n libgphoto2-port12
  libgraphene-1.0-0 libgsound0 libgssdp-1.2-0 libgstreamer-gl1.0-0 libgtk2.0-0
  libgtk2.0-bin libgtk2.0-common libgtop-2.0-11 libgtop2-common libgupnp-1.2-0
  libgupnp-av-1.0-2 libgupnp-dlna-2.0-3 libgusb2 libgweather-3-16
  libgweather-common libhunspell-1.7-0 libhyphen0 libibus-1.0-5 libical3
  libieee1284-3 libimobiledevice6 libinput-bin libinput10 libip6tc2
  libjansson4 libjavascriptcoregtk-4.0-18 libldb2 liblmdb0 liblua5.3-0
  libmaxminddb0 libmbim-glib4 libmbim-proxy libmediaart-2.0-0 libmm-glib0
  libmnl0 libmozjs-68-0 libmtdev1 libmutter-6-0 libmysqlclient21 libndp0
  libnetfilter-conntrack3 libnewt0.52 libnfnetlink0 libnftnl11
  libnl-genl-3-200 libnm0 libnma0 libnotify4 libnss-mdns
  libpackagekit-glib2-18 libpam-fprintd libpam-gnome-keyring libpcap0.8
  libpci3 libphonenumber7 libplist3 libpolkit-agent-1-0 libpolkit-gobject-1-0
  libprotobuf17 libpulse-mainloop-glib0 libpulsedsp libpwquality-common
  libpwquality1 libqmi-glib5 libqmi-proxy libqt5core5a libqt5dbus5 libqt5gui5
  libqt5network5 libqt5printsupport5 libqt5svg5 libqt5widgets5
  librygel-core-2.6-2 librygel-db-2.6-2 librygel-renderer-2.6-2
  librygel-server-2.6-2 libsane libsane-common libsbc1 libsecret-1-0
  libsecret-common libsmbclient libsnapd-glib1 libsnmp-base libsnmp35
  libspeexdsp1 libstartup-notification0 libstemmer0d libtalloc2 libteamdctl0
  libtevent0 libtext-iconv-perl libudisks2-0 libunwind8 libupower-glib3
  libusbmuxd6 libwacom-bin libwacom-common libwacom2 libwbclient0
  libwebkit2gtk-4.0-37 libwebrtc-audio-processing1 libwhoopsie-preferences0
  libwhoopsie0 libwoff1 libwxbase3.0-0v5 libwxgtk3.0-gtk3-0v5 libxatracker2
  libxaw7 libxcb-icccm4 libxcb-image0 libxcb-keysyms1 libxcb-render-util0
  libxcb-res0 libxcb-util1 libxcb-xinerama0 libxcb-xinput0 libxcb-xkb1
  libxcb-xv0 libxfont2 libxkbcommon-x11-0 libxkbfile1 libxklavier16 libxmu6
  libxpm4 libxslt1.1 libxt6 libxtables12 libxvmc1 libyelp0 lsb-release
  mobile-broadband-provider-info modemmanager mousetweaks mutter mutter-common
  mysql-common network-manager network-manager-gnome network-manager-pptp
  p11-kit p11-kit-modules packagekit packagekit-tools pci.ids pinentry-gnome3
  policykit-1 ppp pptp-linux pulseaudio pulseaudio-module-bluetooth
  pulseaudio-utils python-apt-common python3-apport python3-apt
  python3-aptdaemon python3-aptdaemon.gtk3widgets python3-blinker
  python3-cairo python3-certifi python3-cffi-backend python3-chardet
  python3-cryptography python3-cups python3-cupshelpers python3-defer
  python3-distro python3-entrypoints python3-httplib2 python3-ibus-1.0
  python3-idna python3-jwt python3-keyring python3-launchpadlib
  python3-lazr.restfulclient python3-lazr.uri python3-ldb
  python3-macaroonbakery python3-nacl python3-oauthlib python3-problem-report
  python3-protobuf python3-pymacaroons python3-requests
  python3-requests-unixsocket python3-rfc3339 python3-secretstorage
  python3-simplejson python3-systemd python3-talloc python3-tz python3-urllib3
  python3-wadllib qt5-gtk-platformtheme qttranslations5-l10n rtkit rygel
  samba-libs sane-utils session-migration sgml-base sgml-data
  switcheroo-control system-config-printer system-config-printer-common
  system-config-printer-udev ubuntu-docs ubuntu-session ubuntu-wallpapers
  ubuntu-wallpapers-focal udev upower usb-modeswitch usb-modeswitch-data
  usb.ids usbmuxd wamerican whoopsie-preferences wireless-regdb wpasupplicant
  x11-xkb-utils x11-xserver-utils xdg-dbus-proxy xfonts-base xfonts-encodings
  xfonts-utils xml-core xserver-common xserver-xephyr xserver-xorg
  xserver-xorg-core xserver-xorg-input-all xserver-xorg-input-libinput
  xserver-xorg-input-wacom xserver-xorg-legacy xserver-xorg-video-all
  xserver-xorg-video-amdgpu xserver-xorg-video-ati xserver-xorg-video-fbdev
  xserver-xorg-video-intel xserver-xorg-video-nouveau xserver-xorg-video-qxl
  xserver-xorg-video-radeon xserver-xorg-video-vesa xserver-xorg-video-vmware
  xwayland yaru-theme-gnome-shell yelp yelp-xsl zenity zenity-common
0 upgraded, 415 newly installed, 0 to remove and 38 not upgraded.
Need to get 134 MB of archives.
After this operation, 527 MB of additional disk space will be used.
Get:1 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 keyboard-configuration all 1.194ubuntu3 [190 kB]
Get:2 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python-apt-common all 2.0.1ubuntu0.20.04.1 [16.5 kB]
Get:3 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-apt amd64 2.0.1ubuntu0.20.04.1 [154 kB]
Get:4 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libaccountsservice0 amd64 0.6.55-0ubuntu12~20.04.7 [72.4 kB]
Get:5 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libpolkit-gobject-1-0 amd64 0.105-26ubuntu1.3 [39.2 kB]
Get:6 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 accountsservice amd64 0.6.55-0ubuntu12~20.04.7 [61.3 kB]
Get:7 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 language-selector-common all 0.204.2 [224 kB]
Get:8 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/universe amd64 libdouble-conversion3 amd64 3.1.5-4ubuntu1 [37.9 kB]
Get:9 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/universe amd64 libqt5core5a amd64 5.12.8+dfsg-0ubuntu2.1 [2,006 kB]
Get:10 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libevdev2 amd64 1.9.0+dfsg-1ubuntu0.2 [31.6 kB]
Get:11 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libmtdev1 amd64 1.1.5-1.1 [14.2 kB]
Get:12 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libwacom-common all 1.3-2ubuntu3 [45.3 kB]
Get:13 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libwacom2 amd64 1.3-2ubuntu3 [20.0 kB]
Get:14 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libinput-bin amd64 1.15.5-1ubuntu0.3 [19.3 kB]
Get:15 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libinput10 amd64 1.15.5-1ubuntu0.3 [112 kB]
Get:16 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/universe amd64 libqt5dbus5 amd64 5.12.8+dfsg-0ubuntu2.1 [208 kB]
Get:17 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/universe amd64 libqt5network5 amd64 5.12.8+dfsg-0ubuntu2.1 [673 kB]
Get:18 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxcb-icccm4 amd64 0.4.1-1.1 [10.8 kB]
Get:19 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxcb-util1 amd64 0.4.0-0ubuntu3 [11.2 kB]
Get:20 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxcb-image0 amd64 0.4.0-1build1 [12.3 kB]
Get:21 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxcb-keysyms1 amd64 0.4.0-1build1 [8,452 B]
Get:22 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxcb-render-util0 amd64 0.3.9-1build1 [9,912 B]
Get:23 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxcb-xinerama0 amd64 1.14-2 [5,260 B]
Get:24 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxcb-xinput0 amd64 1.14-2 [29.3 kB]
Get:25 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxcb-xkb1 amd64 1.14-2 [29.6 kB]
Get:26 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxkbcommon-x11-0 amd64 0.10.0-1 [13.4 kB]
Get:27 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/universe amd64 libqt5gui5 amd64 5.12.8+dfsg-0ubuntu2.1 [2,971 kB]
Get:28 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/universe amd64 libqt5widgets5 amd64 5.12.8+dfsg-0ubuntu2.1 [2,295 kB]
Get:29 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/universe amd64 libqt5svg5 amd64 5.12.8-0ubuntu1 [131 kB]
Get:30 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libsane-common all 1.0.29-0ubuntu5.2 [277 kB]
Get:31 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libtalloc2 amd64 2.3.3-0ubuntu0.20.04.1 [29.6 kB]
Get:32 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libtevent0 amd64 0.11.0-0ubuntu0.20.04.1 [36.2 kB]
Get:33 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libwbclient0 amd64 2:4.15.13+dfsg-0ubuntu0.20.04.8 [211 kB]
Get:34 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libsnapd-glib1 amd64 1.58-0ubuntu0.20.04.0 [90.1 kB]
Get:35 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libspeexdsp1 amd64 1.2~rc1.2-1.1ubuntu1.20.04.1 [40.4 kB]
Get:36 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libwebrtc-audio-processing1 amd64 0.3.1-0ubuntu3 [263 kB]
Get:37 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libasound2-plugins amd64 1.2.2-1ubuntu1 [66.0 kB]
Get:38 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libpulsedsp amd64 1:13.99.1-1ubuntu3.13 [21.7 kB]
Get:39 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 pulseaudio-utils amd64 1:13.99.1-1ubuntu3.13 [55.0 kB]
Get:40 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 pulseaudio amd64 1:13.99.1-1ubuntu3.13 [814 kB]
Get:41 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 sgml-base all 1.29.1 [12.4 kB]
Get:42 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libjansson4 amd64 2.12-1build1 [28.9 kB]
Get:43 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 liblmdb0 amd64 0.9.24-1 [44.6 kB]
Get:44 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libldb2 amd64 2:2.4.4-0ubuntu0.20.04.2 [143 kB]
Get:45 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-ldb amd64 2:2.4.4-0ubuntu0.20.04.2 [38.2 kB]
Get:46 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-talloc amd64 2.3.3-0ubuntu0.20.04.1 [12.2 kB]
Get:47 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 samba-libs amd64 2:4.15.13+dfsg-0ubuntu0.20.04.8 [5,632 kB]
Get:48 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libsmbclient amd64 2:4.15.13+dfsg-0ubuntu0.20.04.8 [59.4 kB]
Get:49 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 distro-info-data all 0.43ubuntu1.18 [5,020 B]
Get:50 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 kmod amd64 27-1ubuntu2.1 [94.8 kB]
Get:51 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libmnl0 amd64 1.0.4-2 [12.3 kB]
Get:52 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libnewt0.52 amd64 0.52.21-4ubuntu2 [41.1 kB]
Get:53 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libtext-iconv-perl amd64 1.7-7 [13.8 kB]
Get:54 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libxtables12 amd64 1.8.4-3ubuntu2.1 [28.7 kB]
Get:55 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 lsb-release all 11.1.0ubuntu2 [10.6 kB]
Get:56 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-cffi-backend amd64 1.14.0-1build1 [68.7 kB]
Get:57 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-nacl amd64 1.3.0-5 [49.0 kB]
Get:58 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-pymacaroons all 0.13.0-3 [13.2 kB]
Get:59 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 udev amd64 245.4-4ubuntu3.24 [1,366 kB]
Get:60 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libmaxminddb0 amd64 1.4.2-0ubuntu1.20.04.1 [26.2 kB]
Get:61 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 bind9-libs amd64 1:9.18.30-0ubuntu0.20.04.2 [1,155 kB]
Get:62 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 bind9-host amd64 1:9.18.30-0ubuntu0.20.04.2 [47.8 kB]
Get:63 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libip6tc2 amd64 1.8.4-3ubuntu2.1 [19.4 kB]
Get:64 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libnfnetlink0 amd64 1.0.1-3build1 [13.8 kB]
Get:65 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libnetfilter-conntrack3 amd64 1.0.7-2 [41.4 kB]
Get:66 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libnftnl11 amd64 1.1.5-1 [57.8 kB]
Get:67 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 iptables amd64 1.8.4-3ubuntu2.1 [390 kB]
Get:68 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libpcap0.8 amd64 1.9.1-3ubuntu1.20.04.1 [128 kB]
Get:69 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 pci.ids all 0.0~2020.03.20-1 [217 kB]
Get:70 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libpci3 amd64 1:3.6.4-1ubuntu0.20.04.1 [26.9 kB]
Get:71 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 usb.ids all 2020.03.19-1 [176 kB]
Get:72 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 apg amd64 2.2.3.dfsg.1-5 [50.6 kB]
Get:73 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-httplib2 all 0.14.0-1ubuntu1 [28.9 kB]
Get:74 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-problem-report all 2.20.11-0ubuntu27.31 [10.9 kB]
Get:75 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-certifi all 2019.11.28-1 [149 kB]
Get:76 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-chardet all 3.0.4-4build1 [80.4 kB]
Get:77 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-idna all 2.8-1ubuntu0.1 [36.2 kB]
Get:78 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-urllib3 all 1.25.8-2ubuntu0.4 [88.7 kB]
Get:79 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-requests all 2.22.0-2ubuntu1.1 [47.2 kB]
Get:80 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-requests-unixsocket all 0.2.0-2 [7,272 B]
Get:81 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-entrypoints all 0.3-2ubuntu1 [5,740 B]
Get:82 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-cryptography amd64 2.8-3ubuntu0.3 [211 kB]
Get:83 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-secretstorage all 2.3.1-2ubuntu1 [12.4 kB]
Get:84 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-keyring all 18.0.1-2ubuntu1 [28.7 kB]
Get:85 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-lazr.uri all 1.0.3-4build1 [13.9 kB]
Get:86 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-simplejson amd64 3.16.0-2ubuntu2 [50.8 kB]
Get:87 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-wadllib all 1.3.3-3build1 [45.4 kB]
Get:88 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-distro all 1.4.0-1 [14.6 kB]
Get:89 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-blinker all 1.4+dfsg1-0.3ubuntu1 [13.2 kB]
Get:90 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-jwt all 1.7.1-2ubuntu2.1 [18.0 kB]
Get:91 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-oauthlib all 3.1.0-1ubuntu2 [84.8 kB]
Get:92 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-lazr.restfulclient all 0.14.2-2build1 [50.7 kB]
Get:93 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-launchpadlib all 1.10.13-1 [50.0 kB]
Get:94 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-apport all 2.20.11-0ubuntu27.31 [87.0 kB]
Get:95 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 apport all 2.20.11-0ubuntu27.31 [130 kB]
Get:96 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 apport-symptoms all 0.23 [13.5 kB]
Get:97 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libaspell15 amd64 0.60.8-1ubuntu0.1 [328 kB]
Get:98 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 emacsen-common all 3.0.4 [14.9 kB]
Get:99 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 dictionaries-common all 1.28.1 [178 kB]
Get:100 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 aspell amd64 0.60.8-1ubuntu0.1 [88.4 kB]
Get:101 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 aspell-en all 2018.04.16-0-1 [299 kB]
Get:102 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libavahi-core7 amd64 0.7-4ubuntu7.3 [82.1 kB]
Get:103 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libdaemon0 amd64 0.14-7 [13.9 kB]
Get:104 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 avahi-daemon amd64 0.7-4ubuntu7.3 [60.8 kB]
Get:105 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 avahi-utils amd64 0.7-4ubuntu7.3 [24.7 kB]
Get:106 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 bluez amd64 5.53-0ubuntu3.9 [982 kB]
Get:107 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 bolt amd64 0.9.1-2~ubuntu20.04.2 [143 kB]
Get:108 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 bubblewrap amd64 0.4.0-1ubuntu4.1 [35.3 kB]
Get:109 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 cheese-common all 3.34.0-1ubuntu1 [390 kB]
Get:110 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgusb2 amd64 0.3.4-0.1 [22.1 kB]
Get:111 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libcolorhug2 amd64 1.4.4-2 [38.4 kB]
Get:112 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libexif12 amd64 0.6.21-6ubuntu0.4 [78.6 kB]
Get:113 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libxpm4 amd64 1:3.5.12-1ubuntu0.20.04.2 [34.9 kB]
Get:114 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgd3 amd64 2.2.5-5.2ubuntu2.4 [118 kB]
Get:115 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgphoto2-port12 amd64 2.5.25-0ubuntu0.1 [51.8 kB]
Get:116 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgphoto2-6 amd64 2.5.25-0ubuntu0.1 [653 kB]
Get:117 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libieee1284-3 amd64 0.2.11-13build1 [21.0 kB]
Get:118 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 mysql-common all 5.8+1.0.5ubuntu2 [7,496 B]
Get:119 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libmysqlclient21 amd64 8.0.42-0ubuntu0.20.04.1 [1,303 kB]
Get:120 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libsnmp-base all 5.8+dfsg-2ubuntu2.9 [205 kB]
Get:121 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libsnmp35 amd64 5.8+dfsg-2ubuntu2.9 [978 kB]
Get:122 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libsane amd64 1.0.29-0ubuntu5.2 [2,405 kB]
Get:123 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libpolkit-agent-1-0 amd64 0.105-26ubuntu1.3 [15.2 kB]
Get:124 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 policykit-1 amd64 0.105-26ubuntu1.3 [84.1 kB]
Get:125 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 colord-data all 1.4.4-2 [1,093 kB]
Get:126 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 colord amd64 1.4.4-2 [192 kB]
Get:127 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libcrack2 amd64 2.9.6-3.2 [28.3 kB]
Get:128 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 cracklib-runtime amd64 2.9.6-3.2 [140 kB]
Get:129 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libnl-genl-3-200 amd64 3.4.0-1ubuntu0.1 [11.2 kB]
Get:130 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 wireless-regdb all 2024.07.04-0ubuntu1~20.04.1 [9,524 B]
Get:131 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 iw amd64 5.4-1 [94.0 kB]
Get:132 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 crda amd64 3.18-1build1 [63.5 kB]
Get:133 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 dbus-x11 amd64 1.12.16-2ubuntu2.3 [22.6 kB]
Get:134 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 dconf-cli amd64 0.36.0-1 [24.0 kB]
Get:135 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 desktop-file-utils amd64 0.24-1ubuntu3 [50.6 kB]
Get:136 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 dns-root-data all 2024071801~ubuntu0.20.04.1 [6,128 B]
Get:137 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 dnsmasq-base amd64 2.90-0ubuntu0.20.04.1 [350 kB]
Get:138 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xml-core all 0.18+nmu1 [21.6 kB]
Get:139 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 sgml-data all 2.0.11 [171 kB]
Get:140 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 docbook-xml all 4.5-9 [71.2 kB]
Get:141 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 hunspell-en-us all 1:2018.04.16-1 [170 kB]
Get:142 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libhunspell-1.7-0 amd64 1.7.0-2build2 [147 kB]
Get:143 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libenchant-2-2 amd64 2.2.8-1ubuntu0.20.04.1 [45.6 kB]
Get:144 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 enchant-2 amd64 2.2.8-1ubuntu0.20.04.1 [12.1 kB]
Get:145 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libcamel-1.2-62 amd64 3.36.5-0ubuntu1 [433 kB]
Get:146 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libcanberra-gtk3-0 amd64 0.30-7ubuntu1 [7,928 B]
Get:147 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgck-1-0 amd64 3.36.0-2build1 [75.5 kB]
Get:148 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgcr-base-3-1 amd64 3.36.0-2build1 [193 kB]
Get:149 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgoa-1.0-common all 3.36.1-0ubuntu1 [3,752 B]
Get:150 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgoa-1.0-0b amd64 3.36.1-0ubuntu1 [63.4 kB]
Get:151 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgdata-common all 0.17.12-1 [3,392 B]
Get:152 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgdata22 amd64 0.17.12-1 [268 kB]
Get:153 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libsecret-common all 0.20.4-0ubuntu1 [3,940 B]
Get:154 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libsecret-1-0 amd64 0.20.4-0ubuntu1 [110 kB]
Get:155 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 evolution-data-server-common all 3.36.5-0ubuntu1 [18.6 kB]
Get:156 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libedataserver-1.2-24 amd64 3.36.5-0ubuntu1 [244 kB]
Get:157 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libebackend-1.2-10 amd64 3.36.5-0ubuntu1 [103 kB]
Get:158 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libprotobuf17 amd64 3.6.1.3-2ubuntu5.2 [798 kB]
Get:159 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libphonenumber7 amd64 7.1.0-5ubuntu11 [196 kB]
Get:160 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libebook-contacts-1.2-3 amd64 3.36.5-0ubuntu1 [54.5 kB]
Get:161 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libedata-book-1.2-26 amd64 3.36.5-0ubuntu1 [209 kB]
Get:162 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libebook-1.2-20 amd64 3.36.5-0ubuntu1 [79.3 kB]
Get:163 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libical3 amd64 3.0.8-1 [276 kB]
Get:164 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libecal-2.0-1 amd64 3.36.5-0ubuntu1 [145 kB]
Get:165 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libedata-cal-2.0-1 amd64 3.36.5-0ubuntu1 [127 kB]
Get:166 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgcr-ui-3-1 amd64 3.36.0-2build1 [127 kB]
Get:167 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libjavascriptcoregtk-4.0-18 amd64 2.38.6-0ubuntu0.20.04.1 [6,446 kB]
Get:168 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xdg-dbus-proxy amd64 0.1.2-1 [23.0 kB]
Get:169 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgstreamer-gl1.0-0 amd64 1.16.3-0ubuntu1.4 [163 kB]
Get:170 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libhyphen0 amd64 2.8.8-7 [27.0 kB]
Get:171 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libwoff1 amd64 1.0.2-1build2 [42.0 kB]
Get:172 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libxslt1.1 amd64 1.1.34-4ubuntu0.20.04.3 [151 kB]
Get:173 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libwebkit2gtk-4.0-37 amd64 2.38.6-0ubuntu0.20.04.1 [17.5 MB]
Get:174 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libedataserverui-1.2-2 amd64 3.36.5-0ubuntu1 [54.7 kB]
Get:175 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgeocode-glib0 amd64 3.26.2-2 [43.8 kB]
Get:176 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgweather-common all 3.36.1-1~ubuntu20.04.1 [169 kB]
Get:177 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgweather-3-16 amd64 3.36.1-1~ubuntu20.04.1 [68.3 kB]
Get:178 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gcr amd64 3.36.0-2build1 [63.9 kB]
Get:179 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 p11-kit-modules amd64 0.23.20-1ubuntu0.1 [231 kB]
Get:180 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 p11-kit amd64 0.23.20-1ubuntu0.1 [93.8 kB]
Get:181 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 pinentry-gnome3 amd64 1.1.0-3build1 [38.6 kB]
Get:182 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gnome-keyring amd64 3.36.0-1ubuntu1 [613 kB]
Get:183 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 evolution-data-server amd64 3.36.5-0ubuntu1 [679 kB]
Get:184 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgdm1 amd64 3.36.3-0ubuntu0.20.04.4 [63.6 kB]
Get:185 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-gdm-1.0 amd64 3.36.3-0ubuntu0.20.04.4 [9,584 B]
Get:186 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libplist3 amd64 2.1.0-4build2 [31.6 kB]
Get:187 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libusbmuxd6 amd64 2.0.1-2 [19.1 kB]
Get:188 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libimobiledevice6 amd64 1.2.1~git20191129.9f79242-1build1 [65.2 kB]
Get:189 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libupower-glib3 amd64 0.99.11-1build2 [43.2 kB]
Get:190 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 upower amd64 0.99.11-1build2 [104 kB]
Get:191 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gnome-desktop3-data all 3.36.8-0ubuntu1 [21.7 kB]
Get:192 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgnome-desktop-3-19 amd64 3.36.8-0ubuntu1 [115 kB]
Get:193 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gnome-session-bin amd64 3.36.0-2ubuntu1 [117 kB]
Get:194 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gnome-session-common all 3.36.0-2ubuntu1 [5,896 B]
Get:195 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gnome-settings-daemon-common all 3.36.1-0ubuntu1.1 [20.2 kB]
Get:196 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxt6 amd64 1:1.1.5-1 [160 kB]
Get:197 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxmu6 amd64 2:1.1.3-0ubuntu1 [45.8 kB]
Get:198 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxaw7 amd64 2:1.0.13-1 [173 kB]
Get:199 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 x11-xserver-utils amd64 7.7+8 [162 kB]
Get:200 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libmm-glib0 amd64 1.18.6-1~ubuntu20.04.1 [203 kB]
Get:201 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libnotify4 amd64 0.7.9-1ubuntu3.20.04.2 [19.5 kB]
Get:202 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 geoclue-2.0 amd64 2.5.6-0ubuntu1 [91.9 kB]
Get:203 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgeoclue-2-0 amd64 2.5.6-0ubuntu1 [25.1 kB]
Get:204 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libnm0 amd64 1.22.10-1ubuntu2.4 [370 kB]
Get:205 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libpulse-mainloop-glib0 amd64 1:13.99.1-1ubuntu3.13 [11.7 kB]
Get:206 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gnome-settings-daemon amd64 3.36.1-0ubuntu1.1 [308 kB]
Get:207 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-accountsservice-1.0 amd64 0.6.55-0ubuntu12~20.04.7 [4,980 B]
Get:208 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gir1.2-gck-1 amd64 3.36.0-2build1 [10.3 kB]
Get:209 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gir1.2-gcr-3 amd64 3.36.0-2build1 [15.4 kB]
Get:210 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gir1.2-gdesktopenums-3.0 amd64 3.36.0-1ubuntu1 [5,484 B]
Get:211 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gir1.2-geoclue-2.0 amd64 2.5.6-0ubuntu1 [5,892 B]
Get:212 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgnome-bluetooth13 amd64 3.34.3-0ubuntu1 [76.6 kB]
Get:213 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-gnomebluetooth-1.0 amd64 3.34.3-0ubuntu1 [5,592 B]
Get:214 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-gnomedesktop-3.0 amd64 3.36.8-0ubuntu1 [8,352 B]
Get:215 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-gweather-3.0 amd64 3.36.1-1~ubuntu20.04.1 [7,356 B]
Get:216 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libibus-1.0-5 amd64 1.5.22-2ubuntu2.1 [153 kB]
Get:217 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-ibus-1.0 amd64 1.5.22-2ubuntu2.1 [65.9 kB]
Get:218 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 mutter-common all 3.36.9-0ubuntu0.20.04.2 [12.9 kB]
Get:219 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgraphene-1.0-0 amd64 1.10.0-1build2 [42.9 kB]
Get:220 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libstartup-notification0 amd64 0.12-6 [18.8 kB]
Get:221 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxcb-res0 amd64 1.14-2 [6,412 B]
Get:222 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxkbfile1 amd64 1:1.1.0-1 [65.3 kB]
Get:223 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libmutter-6-0 amd64 3.36.9-0ubuntu0.20.04.2 [1,168 kB]
Get:224 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gir1.2-graphene-1.0 amd64 1.10.0-1build2 [10.4 kB]
Get:225 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gir1.2-json-1.0 amd64 1.4.4-2ubuntu2 [8,048 B]
Get:226 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-mutter-6 amd64 3.36.9-0ubuntu0.20.04.2 [109 kB]
Get:227 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-nm-1.0 amd64 1.22.10-1ubuntu2.4 [64.6 kB]
Get:228 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libnma0 amd64 1.8.24-1ubuntu3 [94.8 kB]
Get:229 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-nma-1.0 amd64 1.8.24-1ubuntu3 [5,796 B]
Get:230 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-polkit-1.0 amd64 0.105-26ubuntu1.3 [7,344 B]
Get:231 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-rsvg-2.0 amd64 2.48.9-1ubuntu0.20.04.4 [8,088 B]
Get:232 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-soup-2.4 amd64 2.70.0-1ubuntu0.5 [27.4 kB]
Get:233 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gir1.2-upowerglib-1.0 amd64 0.99.11-1build2 [5,280 B]
Get:234 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libmozjs-68-0 amd64 68.6.0-1ubuntu1 [3,201 kB]
Get:235 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgjs0g amd64 1.64.5-0ubuntu0.20.04.01 [288 kB]
Get:236 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gjs amd64 1.64.5-0ubuntu0.20.04.01 [43.4 kB]
Get:237 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gnome-shell-common all 3.36.9-0ubuntu0.20.04.5 [151 kB]
Get:238 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 ubuntu-wallpapers-focal all 20.04.2-0ubuntu1 [11.8 MB]
Get:239 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 ubuntu-wallpapers all 20.04.2-0ubuntu1 [2,227 kB]
Get:240 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 zenity-common all 3.32.0-5 [311 kB]
Get:241 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 zenity amd64 3.32.0-5 [57.9 kB]
Get:242 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 mutter amd64 3.36.9-0ubuntu0.20.04.2 [129 kB]
Get:243 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgnome-autoar-0-0 amd64 0.2.3-2ubuntu0.4 [26.6 kB]
Get:244 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gnome-shell amd64 3.36.9-0ubuntu0.20.04.5 [782 kB]
Get:245 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 x11-xkb-utils amd64 7.7+5 [158 kB]
Get:246 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 xserver-common all 2:1.20.13-1ubuntu1~20.04.20 [28.1 kB]
Get:247 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libunwind8 amd64 1.2.1-9ubuntu0.1 [47.7 kB]
Get:248 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libfontenc1 amd64 1:1.1.4-0ubuntu1 [14.0 kB]
Get:249 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxfont2 amd64 1:2.0.3-1 [91.7 kB]
Get:250 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 xwayland amd64 2:1.20.13-1ubuntu1~20.04.20 [871 kB]
Get:251 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 yaru-theme-gnome-shell all ********** [106 kB]
Get:252 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 session-migration amd64 0.3.5 [8,988 B]
Get:253 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 ubuntu-session all 3.36.0-2ubuntu1 [5,232 B]
Get:254 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gdm3 amd64 3.36.3-0ubuntu0.20.04.4 [261 kB]
Get:255 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-notify-0.7 amd64 0.7.9-1ubuntu3.20.04.2 [3,588 B]
Get:256 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libpackagekit-glib2-18 amd64 1.1.13-2ubuntu1.1 [104 kB]
Get:257 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-packagekitglib-1.0 amd64 1.1.13-2ubuntu1.1 [21.6 kB]
Get:258 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-secret-1 amd64 0.20.4-0ubuntu1 [8,508 B]
Get:259 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gir1.2-vte-2.91 amd64 0.60.3-0ubuntu1~20.5 [9,504 B]
Get:260 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgnomekbd-common all 3.26.1-1 [6,088 B]
Get:261 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxklavier16 amd64 5.4-4 [43.7 kB]
Get:262 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgnomekbd8 amd64 3.26.1-1 [43.9 kB]
Get:263 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gkbd-capplet amd64 3.26.1-1 [4,904 B]
Get:264 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libcogl20 amd64 1.22.6-1 [293 kB]
Get:265 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libcogl-pango20 amd64 1.22.6-1 [15.7 kB]
Get:266 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libcogl-path20 amd64 1.22.6-1 [31.8 kB]
Get:267 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libclutter-1.0-0 amd64 1.26.4+dfsg-1 [534 kB]
Get:268 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libclutter-gst-3.0-0 amd64 3.0.27-1 [56.9 kB]
Get:269 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libcheese8 amd64 3.34.0-1ubuntu1 [33.3 kB]
Get:270 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libclutter-gtk-1.0-0 amd64 1.8.4-4 [26.1 kB]
Get:271 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gstreamer1.0-clutter-3.0 amd64 3.0.27-1 [6,504 B]
Get:272 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libcheese-gtk25 amd64 3.34.0-1ubuntu1 [27.0 kB]
Get:273 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libcolord-gtk1 amd64 0.2.0-0ubuntu1 [17.7 kB]
Get:274 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgoa-backend-1.0-1 amd64 3.36.1-0ubuntu1 [114 kB]
Get:275 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgsound0 amd64 1.0.2-4 [8,312 B]
Get:276 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgtop2-common all 2.40.0-2 [3,544 B]
Get:277 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgtop-2.0-11 amd64 2.40.0-2 [36.1 kB]
Get:278 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libpwquality-common all 1.4.2-1build1 [7,744 B]
Get:279 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libpwquality1 amd64 1.4.2-1build1 [12.7 kB]
Get:280 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libudisks2-0 amd64 2.8.4-1ubuntu2 [99.4 kB]
Get:281 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libwhoopsie-preferences0 amd64 22 [14.0 kB]
Get:282 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gnome-control-center-data all 1:3.36.5-0ubuntu4.1 [328 kB]
Get:283 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-defer all 1.0.6-2.1 [10.7 kB]
Get:284 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-aptdaemon all 1.1.1+bzr982-0ubuntu32.3 [76.4 kB]
Get:285 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 aptdaemon all 1.1.1+bzr982-0ubuntu32.3 [12.8 kB]
Get:286 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 aptdaemon-data all 1.1.1+bzr982-0ubuntu32.3 [160 kB]
Get:287 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-aptdaemon.gtk3widgets all 1.1.1+bzr982-0ubuntu32.3 [13.1 kB]
Get:288 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 im-config all 0.44-1ubuntu1.3 [25.1 kB]
Get:289 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 language-selector-gnome all 0.204.2 [19.0 kB]
Get:290 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-cups amd64 1.9.73-3build1 [59.5 kB]
Get:291 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-cupshelpers all 1.5.12-0ubuntu1.1 [32.5 kB]
Get:292 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-cairo amd64 1.16.2-2ubuntu2 [56.8 kB]
Get:293 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 system-config-printer-common all 1.5.12-0ubuntu1.1 [108 kB]
Get:294 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 system-config-printer all 1.5.12-0ubuntu1.1 [74.4 kB]
Get:295 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libwhoopsie0 amd64 0.2.69ubuntu0.3 [9,924 B]
Get:296 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 whoopsie-preferences amd64 22 [8,864 B]
Get:297 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gnome-control-center amd64 1:3.36.5-0ubuntu4.1 [1,718 kB]
Get:298 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gnome-control-center-faces all 1:3.36.5-0ubuntu4.1 [1,216 kB]
Get:299 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gnome-keyring-pkcs11 amd64 3.36.0-1ubuntu1 [28.0 kB]
Get:300 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gnome-menus amd64 3.36.0-1ubuntu1 [10.1 kB]
Get:301 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-protobuf amd64 3.6.1.3-2ubuntu5.2 [298 kB]
Get:302 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-tz all 2019.3-1ubuntu0.20.04.0 [24.5 kB]
Get:303 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-rfc3339 all 1.1-2 [6,808 B]
Get:304 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-macaroonbakery all 1.3.1-1 [63.7 kB]
Get:305 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gnome-online-accounts amd64 3.36.1-0ubuntu1 [83.5 kB]
Get:306 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 gnome-startup-applications amd64 3.36.0-2ubuntu1 [31.0 kB]
Get:307 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libyelp0 amd64 3.36.2-0ubuntu1.1 [93.8 kB]
Get:308 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 yelp-xsl all 3.36.0-1ubuntu0.1 [180 kB]
Get:309 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 yelp amd64 3.36.2-0ubuntu1.1 [527 kB]
Get:310 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 ubuntu-docs all 20.04.3 [249 kB]
Get:311 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gnome-user-docs all 3.36.2+git20200704-0ubuntu0.1 [1,740 kB]
Get:312 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/universe amd64 aglfn all 1.7+git20191031.4036a9c-2 [30.6 kB]
Get:313 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/universe amd64 gnuplot-data all 5.2.8+dfsg1-2 [56.5 kB]
Get:314 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 liblua5.3-0 amd64 5.3.3-1.1ubuntu2 [116 kB]
Get:315 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/universe amd64 libqt5printsupport5 amd64 5.12.8+dfsg-0ubuntu2.1 [193 kB]
Get:316 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/universe amd64 libwxbase3.0-0v5 amd64 3.0.4+dfsg-15build1 [982 kB]
Get:317 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/universe amd64 libwxgtk3.0-gtk3-0v5 amd64 3.0.4+dfsg-15build1 [4,359 kB]
Get:318 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/universe amd64 gnuplot-qt amd64 5.2.8+dfsg1-2 [1,034 kB]
Get:319 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/universe amd64 gnuplot all 5.2.8+dfsg1-2 [3,792 B]
Get:320 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gstreamer1.0-gl amd64 1.16.3-0ubuntu1.4 [109 kB]
Get:321 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 gstreamer1.0-pulseaudio amd64 1.16.3-0ubuntu1.3 [93.7 kB]
Get:322 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 ibus-data all 1.5.22-2ubuntu2.1 [4,533 kB]
Get:323 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 python3-ibus-1.0 all 1.5.22-2ubuntu2.1 [7,120 B]
Get:324 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 ibus amd64 1.5.22-2ubuntu2.1 [288 kB]
Get:325 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgtk2.0-common all 2.24.32-4ubuntu4.1 [126 kB]
Get:326 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgtk2.0-0 amd64 2.24.32-4ubuntu4.1 [1,789 kB]
Get:327 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 ibus-gtk amd64 1.5.22-2ubuntu2.1 [15.2 kB]
Get:328 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 ibus-gtk3 amd64 1.5.22-2ubuntu2.1 [15.8 kB]
Get:329 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 iio-sensor-proxy amd64 2.8-1ubuntu2 [35.8 kB]
Get:330 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libdbusmenu-glib4 amd64 16.04.1+18.10.20180917-0ubuntu6 [41.2 kB]
Get:331 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libdbusmenu-gtk3-4 amd64 16.04.1+18.10.20180917-0ubuntu6 [27.7 kB]
Get:332 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libappindicator3-1 amd64 12.10.1+20.04.20200408.1-0ubuntu1 [22.9 kB]
Get:333 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libstemmer0d amd64 0+svn585-2 [61.7 kB]
Get:334 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libappstream4 amd64 0.12.10-2 [129 kB]
Get:335 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libbluetooth3 amd64 5.53-0ubuntu3.9 [60.5 kB]
Get:336 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libcanberra-pulse amd64 0.30-7ubuntu1 [11.9 kB]
Get:337 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libclutter-1.0-common all 1.26.4+dfsg-1 [4,236 B]
Get:338 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libcogl-common all 1.22.6-1 [177 kB]
Get:339 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libfprint-2-2 amd64 1:1.90.2+tod1-0ubuntu1~20.04.10 [212 kB]
Get:340 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgail18 amd64 2.24.32-4ubuntu4.1 [14.8 kB]
Get:341 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgail-common amd64 2.24.32-4ubuntu4.1 [115 kB]
Get:342 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgee-0.8-2 amd64 0.20.3-1 [216 kB]
Get:343 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgphoto2-l10n all 2.5.25-0ubuntu0.1 [12.2 kB]
Get:344 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgssdp-1.2-0 amd64 1.2.3-0ubuntu0.20.04.1 [37.1 kB]
Get:345 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgtk2.0-bin amd64 2.24.32-4ubuntu4.1 [7,728 B]
Get:346 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libgupnp-1.2-0 amd64 1.2.4-0ubuntu1 [81.3 kB]
Get:347 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgupnp-av-1.0-2 amd64 0.12.11-2 [67.5 kB]
Get:348 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libgupnp-dlna-2.0-3 amd64 0.10.5-4 [50.0 kB]
Get:349 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libmbim-glib4 amd64 1.26.2-1~ubuntu20.04.1 [123 kB]
Get:350 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libmbim-proxy amd64 1.26.2-1~ubuntu20.04.1 [6,012 B]
Get:351 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libmediaart-2.0-0 amd64 1.9.4-2 [22.0 kB]
Get:352 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libndp0 amd64 1.7-0ubuntu1.1 [11.1 kB]
Get:353 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libnss-mdns amd64 0.14.1-1ubuntu1 [22.9 kB]
Get:354 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libpam-gnome-keyring amd64 3.36.0-1ubuntu1 [23.3 kB]
Get:355 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libqmi-glib5 amd64 1.30.4-1~ubuntu20.04.1 [642 kB]
Get:356 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libqmi-proxy amd64 1.30.4-1~ubuntu20.04.1 [5,908 B]
Get:357 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libteamdctl0 amd64 1.30-1 [11.8 kB]
Get:358 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libwacom-bin amd64 1.3-2ubuntu3 [5,484 B]
Get:359 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libxatracker2 amd64 21.2.6-0ubuntu0.1~20.04.2 [1,803 kB]
Get:360 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxcb-xv0 amd64 1.14-2 [9,192 B]
Get:361 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libxvmc1 amd64 2:1.0.12-2 [14.2 kB]
Get:362 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 modemmanager amd64 1.18.6-1~ubuntu20.04.1 [895 kB]
Get:363 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 mousetweaks amd64 3.32.0-2 [37.9 kB]
Get:364 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 wpasupplicant amd64 2:2.9-1ubuntu4.6 [1,185 kB]
Get:365 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 network-manager amd64 1.22.10-1ubuntu2.4 [1,854 kB]
Get:366 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 network-manager-gnome amd64 1.8.24-1ubuntu3 [324 kB]
Get:367 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 ppp amd64 2.4.7-2+4.1ubuntu5.1 [328 kB]
Get:368 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 pptp-linux amd64 1.10.0-1build1 [42.0 kB]
Get:369 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 network-manager-pptp amd64 1.2.8-2 [30.1 kB]
Get:370 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 packagekit amd64 1.1.13-2ubuntu1.1 [408 kB]
Get:371 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 packagekit-tools amd64 1.1.13-2ubuntu1.1 [32.8 kB]
Get:372 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 python3-systemd amd64 234-3build2 [36.5 kB]
Get:373 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/universe amd64 qt5-gtk-platformtheme amd64 5.12.8+dfsg-0ubuntu2.1 [124 kB]
Get:374 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/universe amd64 qttranslations5-l10n all 5.12.8-0ubuntu1 [1,486 kB]
Get:375 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 rtkit amd64 0.12-4 [34.1 kB]
Get:376 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 sane-utils amd64 1.0.29-0ubuntu5.2 [201 kB]
Get:377 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 switcheroo-control amd64 2.1-1 [13.8 kB]
Get:378 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 system-config-printer-udev amd64 1.5.12-0ubuntu1.1 [19.7 kB]
Get:379 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 usbmuxd amd64 1.1.1~git20191130.9af2b12-1 [38.4 kB]
Get:380 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 wamerican all 2018.04.16-1 [210 kB]
Get:381 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xfonts-encodings all 1:1.0.5-0ubuntu1 [573 kB]
Get:382 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xfonts-utils amd64 1:7.7+6 [91.5 kB]
Get:383 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xfonts-base all 1:1.0.5 [5,896 kB]
Get:384 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 xserver-xephyr amd64 2:1.20.13-1ubuntu1~20.04.20 [917 kB]
Get:385 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 xserver-xorg-core amd64 2:1.20.13-1ubuntu1~20.04.20 [1,342 kB]
Get:386 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg-input-libinput amd64 0.29.0-1 [33.7 kB]
Get:387 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg-input-all amd64 1:7.7+19ubuntu14 [4,016 B]
Get:388 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg-input-wacom amd64 1:0.39.0-0ubuntu1 [91.4 kB]
Get:389 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg amd64 1:7.7+19ubuntu14 [65.2 kB]
Get:390 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 xserver-xorg-video-amdgpu amd64 19.1.0-1ubuntu0.1 [68.5 kB]
Get:391 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg-video-radeon amd64 1:19.1.0-1 [153 kB]
Get:392 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg-video-ati amd64 1:19.1.0-1 [7,112 B]
Get:393 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg-video-fbdev amd64 1:0.5.0-1ubuntu1 [12.2 kB]
Get:394 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg-video-nouveau amd64 1:1.0.16-1 [89.4 kB]
Get:395 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg-video-vesa amd64 1:2.4.0-2 [15.0 kB]
Get:396 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg-video-vmware amd64 1:13.3.0-3 [73.6 kB]
Get:397 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg-video-all amd64 1:7.7+19ubuntu14 [4,068 B]
Get:398 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg-video-intel amd64 2:2.99.917+git20200226-1 [737 kB]
Get:399 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 xserver-xorg-video-qxl amd64 0.1.5+git20200331-1 [83.1 kB]
Get:400 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 cups-pk-helper amd64 0.2.6-1ubuntu3 [52.1 kB]
Get:401 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 fprintd amd64 1.90.9-1~ubuntu20.04.1 [80.8 kB]
Get:402 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 ippusbxd amd64 1.34-2ubuntu1 [36.0 kB]
Get:403 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libcanberra-gtk3-module amd64 0.30-7ubuntu1 [10.1 kB]
Get:404 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 libpam-fprintd amd64 1.90.9-1~ubuntu20.04.1 [13.2 kB]
Get:405 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 librygel-core-2.6-2 amd64 0.38.3-1ubuntu1 [99.8 kB]
Get:406 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 librygel-db-2.6-2 amd64 0.38.3-1ubuntu1 [22.0 kB]
Get:407 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 librygel-renderer-2.6-2 amd64 0.38.3-1ubuntu1 [46.5 kB]
Get:408 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 librygel-server-2.6-2 amd64 0.38.3-1ubuntu1 [185 kB]
Get:409 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 libsbc1 amd64 1.4-1 [31.9 kB]
Get:410 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 mobile-broadband-provider-info all 20190618-3 [61.9 kB]
Get:411 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 pulseaudio-module-bluetooth amd64 1:13.99.1-1ubuntu3.13 [60.4 kB]
Get:412 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 rygel amd64 0.38.3-1ubuntu1 [299 kB]
Get:413 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 usb-modeswitch-data all 20191128-3 [32.3 kB]
Get:414 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal/main amd64 usb-modeswitch amd64 2.5.2+repack0-2ubuntu3 [53.1 kB]
Get:415 https://mirrors.tuna.tsinghua.edu.cn/ubuntu focal-updates/main amd64 xserver-xorg-legacy amd64 2:1.20.13-1ubuntu1~20.04.20 [33.7 kB]
debconf: delaying package configuration, since apt-utils is not installed
Fetched 134 MB in 8min 18s (270 kB/s)
Selecting previously unselected package keyboard-configuration.
(Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 69653 files and directories currently installed.)
Preparing to unpack .../000-keyboard-configuration_1.194ubuntu3_all.deb ...
Unpacking keyboard-configuration (1.194ubuntu3) ...
Selecting previously unselected package python-apt-common.
Preparing to unpack .../001-python-apt-common_2.0.1ubuntu0.20.04.1_all.deb ...
Unpacking python-apt-common (2.0.1ubuntu0.20.04.1) ...
Selecting previously unselected package python3-apt.
Preparing to unpack .../002-python3-apt_2.0.1ubuntu0.20.04.1_amd64.deb ...
Unpacking python3-apt (2.0.1ubuntu0.20.04.1) ...
Selecting previously unselected package libaccountsservice0:amd64.
Preparing to unpack .../003-libaccountsservice0_0.6.55-0ubuntu12~20.04.7_amd64.deb ...
Unpacking libaccountsservice0:amd64 (0.6.55-0ubuntu12~20.04.7) ...
Selecting previously unselected package libpolkit-gobject-1-0:amd64.
Preparing to unpack .../004-libpolkit-gobject-1-0_0.105-26ubuntu1.3_amd64.deb ...
Unpacking libpolkit-gobject-1-0:amd64 (0.105-26ubuntu1.3) ...
Selecting previously unselected package accountsservice.
Preparing to unpack .../005-accountsservice_0.6.55-0ubuntu12~20.04.7_amd64.deb ...
Unpacking accountsservice (0.6.55-0ubuntu12~20.04.7) ...
Selecting previously unselected package language-selector-common.
Preparing to unpack .../006-language-selector-common_0.204.2_all.deb ...
Unpacking language-selector-common (0.204.2) ...
Selecting previously unselected package libdouble-conversion3:amd64.
Preparing to unpack .../007-libdouble-conversion3_3.1.5-4ubuntu1_amd64.deb ...
Unpacking libdouble-conversion3:amd64 (3.1.5-4ubuntu1) ...
Selecting previously unselected package libqt5core5a:amd64.
Preparing to unpack .../008-libqt5core5a_5.12.8+dfsg-0ubuntu2.1_amd64.deb ...
Unpacking libqt5core5a:amd64 (5.12.8+dfsg-0ubuntu2.1) ...
Selecting previously unselected package libevdev2:amd64.
Preparing to unpack .../009-libevdev2_1.9.0+dfsg-1ubuntu0.2_amd64.deb ...
Unpacking libevdev2:amd64 (1.9.0+dfsg-1ubuntu0.2) ...
Selecting previously unselected package libmtdev1:amd64.
Preparing to unpack .../010-libmtdev1_1.1.5-1.1_amd64.deb ...
Unpacking libmtdev1:amd64 (1.1.5-1.1) ...
Selecting previously unselected package libwacom-common.
Preparing to unpack .../011-libwacom-common_1.3-2ubuntu3_all.deb ...
Unpacking libwacom-common (1.3-2ubuntu3) ...
Selecting previously unselected package libwacom2:amd64.
Preparing to unpack .../012-libwacom2_1.3-2ubuntu3_amd64.deb ...
Unpacking libwacom2:amd64 (1.3-2ubuntu3) ...
Selecting previously unselected package libinput-bin.
Preparing to unpack .../013-libinput-bin_1.15.5-1ubuntu0.3_amd64.deb ...
Unpacking libinput-bin (1.15.5-1ubuntu0.3) ...
Selecting previously unselected package libinput10:amd64.
Preparing to unpack .../014-libinput10_1.15.5-1ubuntu0.3_amd64.deb ...
Unpacking libinput10:amd64 (1.15.5-1ubuntu0.3) ...
Selecting previously unselected package libqt5dbus5:amd64.
Preparing to unpack .../015-libqt5dbus5_5.12.8+dfsg-0ubuntu2.1_amd64.deb ...
Unpacking libqt5dbus5:amd64 (5.12.8+dfsg-0ubuntu2.1) ...
Selecting previously unselected package libqt5network5:amd64.
Preparing to unpack .../016-libqt5network5_5.12.8+dfsg-0ubuntu2.1_amd64.deb ...
Unpacking libqt5network5:amd64 (5.12.8+dfsg-0ubuntu2.1) ...
Selecting previously unselected package libxcb-icccm4:amd64.
Preparing to unpack .../017-libxcb-icccm4_0.4.1-1.1_amd64.deb ...
Unpacking libxcb-icccm4:amd64 (0.4.1-1.1) ...
Selecting previously unselected package libxcb-util1:amd64.
Preparing to unpack .../018-libxcb-util1_0.4.0-0ubuntu3_amd64.deb ...
Unpacking libxcb-util1:amd64 (0.4.0-0ubuntu3) ...
Selecting previously unselected package libxcb-image0:amd64.
Preparing to unpack .../019-libxcb-image0_0.4.0-1build1_amd64.deb ...
Unpacking libxcb-image0:amd64 (0.4.0-1build1) ...
Selecting previously unselected package libxcb-keysyms1:amd64.
Preparing to unpack .../020-libxcb-keysyms1_0.4.0-1build1_amd64.deb ...
Unpacking libxcb-keysyms1:amd64 (0.4.0-1build1) ...
Selecting previously unselected package libxcb-render-util0:amd64.
Preparing to unpack .../021-libxcb-render-util0_0.3.9-1build1_amd64.deb ...
Unpacking libxcb-render-util0:amd64 (0.3.9-1build1) ...
Selecting previously unselected package libxcb-xinerama0:amd64.
Preparing to unpack .../022-libxcb-xinerama0_1.14-2_amd64.deb ...
Unpacking libxcb-xinerama0:amd64 (1.14-2) ...
Selecting previously unselected package libxcb-xinput0:amd64.
Preparing to unpack .../023-libxcb-xinput0_1.14-2_amd64.deb ...
Unpacking libxcb-xinput0:amd64 (1.14-2) ...
Selecting previously unselected package libxcb-xkb1:amd64.
Preparing to unpack .../024-libxcb-xkb1_1.14-2_amd64.deb ...
Unpacking libxcb-xkb1:amd64 (1.14-2) ...
Selecting previously unselected package libxkbcommon-x11-0:amd64.
Preparing to unpack .../025-libxkbcommon-x11-0_0.10.0-1_amd64.deb ...
Unpacking libxkbcommon-x11-0:amd64 (0.10.0-1) ...
Selecting previously unselected package libqt5gui5:amd64.
Preparing to unpack .../026-libqt5gui5_5.12.8+dfsg-0ubuntu2.1_amd64.deb ...
Unpacking libqt5gui5:amd64 (5.12.8+dfsg-0ubuntu2.1) ...
Selecting previously unselected package libqt5widgets5:amd64.
Preparing to unpack .../027-libqt5widgets5_5.12.8+dfsg-0ubuntu2.1_amd64.deb ...
Unpacking libqt5widgets5:amd64 (5.12.8+dfsg-0ubuntu2.1) ...
Selecting previously unselected package libqt5svg5:amd64.
Preparing to unpack .../028-libqt5svg5_5.12.8-0ubuntu1_amd64.deb ...
Unpacking libqt5svg5:amd64 (5.12.8-0ubuntu1) ...
Selecting previously unselected package libsane-common.
Preparing to unpack .../029-libsane-common_1.0.29-0ubuntu5.2_all.deb ...
Unpacking libsane-common (1.0.29-0ubuntu5.2) ...
Selecting previously unselected package libtalloc2:amd64.
Preparing to unpack .../030-libtalloc2_2.3.3-0ubuntu0.20.04.1_amd64.deb ...
Unpacking libtalloc2:amd64 (2.3.3-0ubuntu0.20.04.1) ...
Selecting previously unselected package libtevent0:amd64.
Preparing to unpack .../031-libtevent0_0.11.0-0ubuntu0.20.04.1_amd64.deb ...
Unpacking libtevent0:amd64 (0.11.0-0ubuntu0.20.04.1) ...
Selecting previously unselected package libwbclient0:amd64.
Preparing to unpack .../032-libwbclient0_2%3a4.15.13+dfsg-0ubuntu0.20.04.8_amd64.deb ...
Unpacking libwbclient0:amd64 (2:4.15.13+dfsg-0ubuntu0.20.04.8) ...
Selecting previously unselected package libsnapd-glib1:amd64.
Preparing to unpack .../033-libsnapd-glib1_1.58-0ubuntu0.20.04.0_amd64.deb ...
Unpacking libsnapd-glib1:amd64 (1.58-0ubuntu0.20.04.0) ...
Selecting previously unselected package libspeexdsp1:amd64.
Preparing to unpack .../034-libspeexdsp1_1.2~rc1.2-1.1ubuntu1.20.04.1_amd64.deb ...
Unpacking libspeexdsp1:amd64 (1.2~rc1.2-1.1ubuntu1.20.04.1) ...
Selecting previously unselected package libwebrtc-audio-processing1:amd64.
Preparing to unpack .../035-libwebrtc-audio-processing1_0.3.1-0ubuntu3_amd64.deb ...
Unpacking libwebrtc-audio-processing1:amd64 (0.3.1-0ubuntu3) ...
Selecting previously unselected package libasound2-plugins:amd64.
Preparing to unpack .../036-libasound2-plugins_1.2.2-1ubuntu1_amd64.deb ...
Unpacking libasound2-plugins:amd64 (1.2.2-1ubuntu1) ...
Selecting previously unselected package libpulsedsp:amd64.
Preparing to unpack .../037-libpulsedsp_1%3a13.99.1-1ubuntu3.13_amd64.deb ...
Unpacking libpulsedsp:amd64 (1:13.99.1-1ubuntu3.13) ...
Selecting previously unselected package pulseaudio-utils.
Preparing to unpack .../038-pulseaudio-utils_1%3a13.99.1-1ubuntu3.13_amd64.deb ...
Unpacking pulseaudio-utils (1:13.99.1-1ubuntu3.13) ...
Selecting previously unselected package pulseaudio.
Preparing to unpack .../039-pulseaudio_1%3a13.99.1-1ubuntu3.13_amd64.deb ...
Unpacking pulseaudio (1:13.99.1-1ubuntu3.13) ...
Selecting previously unselected package sgml-base.
Preparing to unpack .../040-sgml-base_1.29.1_all.deb ...
Unpacking sgml-base (1.29.1) ...
Selecting previously unselected package libjansson4:amd64.
Preparing to unpack .../041-libjansson4_2.12-1build1_amd64.deb ...
Unpacking libjansson4:amd64 (2.12-1build1) ...
Selecting previously unselected package liblmdb0:amd64.
Preparing to unpack .../042-liblmdb0_0.9.24-1_amd64.deb ...
Unpacking liblmdb0:amd64 (0.9.24-1) ...
Selecting previously unselected package libldb2:amd64.
Preparing to unpack .../043-libldb2_2%3a2.4.4-0ubuntu0.20.04.2_amd64.deb ...
Unpacking libldb2:amd64 (2:2.4.4-0ubuntu0.20.04.2) ...
Selecting previously unselected package python3-ldb.
Preparing to unpack .../044-python3-ldb_2%3a2.4.4-0ubuntu0.20.04.2_amd64.deb ...
Unpacking python3-ldb (2:2.4.4-0ubuntu0.20.04.2) ...
Selecting previously unselected package python3-talloc:amd64.
Preparing to unpack .../045-python3-talloc_2.3.3-0ubuntu0.20.04.1_amd64.deb ...
Unpacking python3-talloc:amd64 (2.3.3-0ubuntu0.20.04.1) ...
Selecting previously unselected package samba-libs:amd64.
Preparing to unpack .../046-samba-libs_2%3a4.15.13+dfsg-0ubuntu0.20.04.8_amd64.deb ...
Unpacking samba-libs:amd64 (2:4.15.13+dfsg-0ubuntu0.20.04.8) ...
Selecting previously unselected package libsmbclient:amd64.
Preparing to unpack .../047-libsmbclient_2%3a4.15.13+dfsg-0ubuntu0.20.04.8_amd64.deb ...
Unpacking libsmbclient:amd64 (2:4.15.13+dfsg-0ubuntu0.20.04.8) ...
Selecting previously unselected package distro-info-data.
Preparing to unpack .../048-distro-info-data_0.43ubuntu1.18_all.deb ...
Unpacking distro-info-data (0.43ubuntu1.18) ...
Selecting previously unselected package kmod.
Preparing to unpack .../049-kmod_27-1ubuntu2.1_amd64.deb ...
Unpacking kmod (27-1ubuntu2.1) ...
Selecting previously unselected package libmnl0:amd64.
Preparing to unpack .../050-libmnl0_1.0.4-2_amd64.deb ...
Unpacking libmnl0:amd64 (1.0.4-2) ...
Selecting previously unselected package libnewt0.52:amd64.
Preparing to unpack .../051-libnewt0.52_0.52.21-4ubuntu2_amd64.deb ...
Unpacking libnewt0.52:amd64 (0.52.21-4ubuntu2) ...
Selecting previously unselected package libtext-iconv-perl.
Preparing to unpack .../052-libtext-iconv-perl_1.7-7_amd64.deb ...
Unpacking libtext-iconv-perl (1.7-7) ...
Selecting previously unselected package libxtables12:amd64.
Preparing to unpack .../053-libxtables12_1.8.4-3ubuntu2.1_amd64.deb ...
Unpacking libxtables12:amd64 (1.8.4-3ubuntu2.1) ...
Selecting previously unselected package lsb-release.
Preparing to unpack .../054-lsb-release_11.1.0ubuntu2_all.deb ...
Unpacking lsb-release (11.1.0ubuntu2) ...
Selecting previously unselected package python3-cffi-backend.
Preparing to unpack .../055-python3-cffi-backend_1.14.0-1build1_amd64.deb ...
Unpacking python3-cffi-backend (1.14.0-1build1) ...
Selecting previously unselected package python3-nacl.
Preparing to unpack .../056-python3-nacl_1.3.0-5_amd64.deb ...
Unpacking python3-nacl (1.3.0-5) ...
Selecting previously unselected package python3-pymacaroons.
Preparing to unpack .../057-python3-pymacaroons_0.13.0-3_all.deb ...
Unpacking python3-pymacaroons (0.13.0-3) ...
Selecting previously unselected package udev.
Preparing to unpack .../058-udev_245.4-4ubuntu3.24_amd64.deb ...
Unpacking udev (245.4-4ubuntu3.24) ...
Selecting previously unselected package libmaxminddb0:amd64.
Preparing to unpack .../059-libmaxminddb0_1.4.2-0ubuntu1.20.04.1_amd64.deb ...
Unpacking libmaxminddb0:amd64 (1.4.2-0ubuntu1.20.04.1) ...
Selecting previously unselected package bind9-libs:amd64.
Preparing to unpack .../060-bind9-libs_1%3a9.18.30-0ubuntu0.20.04.2_amd64.deb ...
Unpacking bind9-libs:amd64 (1:9.18.30-0ubuntu0.20.04.2) ...
Selecting previously unselected package bind9-host.
Preparing to unpack .../061-bind9-host_1%3a9.18.30-0ubuntu0.20.04.2_amd64.deb ...
Unpacking bind9-host (1:9.18.30-0ubuntu0.20.04.2) ...
Selecting previously unselected package libip6tc2:amd64.
Preparing to unpack .../062-libip6tc2_1.8.4-3ubuntu2.1_amd64.deb ...
Unpacking libip6tc2:amd64 (1.8.4-3ubuntu2.1) ...
Selecting previously unselected package libnfnetlink0:amd64.
Preparing to unpack .../063-libnfnetlink0_1.0.1-3build1_amd64.deb ...
Unpacking libnfnetlink0:amd64 (1.0.1-3build1) ...
Selecting previously unselected package libnetfilter-conntrack3:amd64.
Preparing to unpack .../064-libnetfilter-conntrack3_1.0.7-2_amd64.deb ...
Unpacking libnetfilter-conntrack3:amd64 (1.0.7-2) ...
Selecting previously unselected package libnftnl11:amd64.
Preparing to unpack .../065-libnftnl11_1.1.5-1_amd64.deb ...
Unpacking libnftnl11:amd64 (1.1.5-1) ...
Selecting previously unselected package iptables.
Preparing to unpack .../066-iptables_1.8.4-3ubuntu2.1_amd64.deb ...
Unpacking iptables (1.8.4-3ubuntu2.1) ...
Selecting previously unselected package libpcap0.8:amd64.
Preparing to unpack .../067-libpcap0.8_1.9.1-3ubuntu1.20.04.1_amd64.deb ...
Unpacking libpcap0.8:amd64 (1.9.1-3ubuntu1.20.04.1) ...
Selecting previously unselected package pci.ids.
Preparing to unpack .../068-pci.ids_0.0~2020.03.20-1_all.deb ...
Unpacking pci.ids (0.0~2020.03.20-1) ...
Selecting previously unselected package libpci3:amd64.
Preparing to unpack .../069-libpci3_1%3a3.6.4-1ubuntu0.20.04.1_amd64.deb ...
Unpacking libpci3:amd64 (1:3.6.4-1ubuntu0.20.04.1) ...
Selecting previously unselected package usb.ids.
Preparing to unpack .../070-usb.ids_2020.03.19-1_all.deb ...
Unpacking usb.ids (2020.03.19-1) ...
Selecting previously unselected package apg.
Preparing to unpack .../071-apg_2.2.3.dfsg.1-5_amd64.deb ...
Unpacking apg (2.2.3.dfsg.1-5) ...
Selecting previously unselected package python3-httplib2.
Preparing to unpack .../072-python3-httplib2_0.14.0-1ubuntu1_all.deb ...
Unpacking python3-httplib2 (0.14.0-1ubuntu1) ...
Selecting previously unselected package python3-problem-report.
Preparing to unpack .../073-python3-problem-report_2.20.11-0ubuntu27.31_all.deb ...
Unpacking python3-problem-report (2.20.11-0ubuntu27.31) ...
Selecting previously unselected package python3-certifi.
Preparing to unpack .../074-python3-certifi_2019.11.28-1_all.deb ...
Unpacking python3-certifi (2019.11.28-1) ...
Selecting previously unselected package python3-chardet.
Preparing to unpack .../075-python3-chardet_3.0.4-4build1_all.deb ...
Unpacking python3-chardet (3.0.4-4build1) ...
Selecting previously unselected package python3-idna.
Preparing to unpack .../076-python3-idna_2.8-1ubuntu0.1_all.deb ...
Unpacking python3-idna (2.8-1ubuntu0.1) ...
Selecting previously unselected package python3-urllib3.
Preparing to unpack .../077-python3-urllib3_1.25.8-2ubuntu0.4_all.deb ...
Unpacking python3-urllib3 (1.25.8-2ubuntu0.4) ...
Selecting previously unselected package python3-requests.
Preparing to unpack .../078-python3-requests_2.22.0-2ubuntu1.1_all.deb ...
Unpacking python3-requests (2.22.0-2ubuntu1.1) ...
Selecting previously unselected package python3-requests-unixsocket.
Preparing to unpack .../079-python3-requests-unixsocket_0.2.0-2_all.deb ...
Unpacking python3-requests-unixsocket (0.2.0-2) ...
Selecting previously unselected package python3-entrypoints.
Preparing to unpack .../080-python3-entrypoints_0.3-2ubuntu1_all.deb ...
Unpacking python3-entrypoints (0.3-2ubuntu1) ...
Selecting previously unselected package python3-cryptography.
Preparing to unpack .../081-python3-cryptography_2.8-3ubuntu0.3_amd64.deb ...
Unpacking python3-cryptography (2.8-3ubuntu0.3) ...
Selecting previously unselected package python3-secretstorage.
Preparing to unpack .../082-python3-secretstorage_2.3.1-2ubuntu1_all.deb ...
Unpacking python3-secretstorage (2.3.1-2ubuntu1) ...
Selecting previously unselected package python3-keyring.
Preparing to unpack .../083-python3-keyring_18.0.1-2ubuntu1_all.deb ...
Unpacking python3-keyring (18.0.1-2ubuntu1) ...
Selecting previously unselected package python3-lazr.uri.
Preparing to unpack .../084-python3-lazr.uri_1.0.3-4build1_all.deb ...
Unpacking python3-lazr.uri (1.0.3-4build1) ...
Selecting previously unselected package python3-simplejson.
Preparing to unpack .../085-python3-simplejson_3.16.0-2ubuntu2_amd64.deb ...
Unpacking python3-simplejson (3.16.0-2ubuntu2) ...
Selecting previously unselected package python3-wadllib.
Preparing to unpack .../086-python3-wadllib_1.3.3-3build1_all.deb ...
Unpacking python3-wadllib (1.3.3-3build1) ...
Selecting previously unselected package python3-distro.
Preparing to unpack .../087-python3-distro_1.4.0-1_all.deb ...
Unpacking python3-distro (1.4.0-1) ...
Selecting previously unselected package python3-blinker.
Preparing to unpack .../088-python3-blinker_1.4+dfsg1-0.3ubuntu1_all.deb ...
Unpacking python3-blinker (1.4+dfsg1-0.3ubuntu1) ...
Selecting previously unselected package python3-jwt.
Preparing to unpack .../089-python3-jwt_1.7.1-2ubuntu2.1_all.deb ...
Unpacking python3-jwt (1.7.1-2ubuntu2.1) ...
Selecting previously unselected package python3-oauthlib.
Preparing to unpack .../090-python3-oauthlib_3.1.0-1ubuntu2_all.deb ...
Unpacking python3-oauthlib (3.1.0-1ubuntu2) ...
Selecting previously unselected package python3-lazr.restfulclient.
Preparing to unpack .../091-python3-lazr.restfulclient_0.14.2-2build1_all.deb ...
Unpacking python3-lazr.restfulclient (0.14.2-2build1) ...
Selecting previously unselected package python3-launchpadlib.
Preparing to unpack .../092-python3-launchpadlib_1.10.13-1_all.deb ...
Unpacking python3-launchpadlib (1.10.13-1) ...
Selecting previously unselected package python3-apport.
Preparing to unpack .../093-python3-apport_2.20.11-0ubuntu27.31_all.deb ...
Unpacking python3-apport (2.20.11-0ubuntu27.31) ...
Selecting previously unselected package apport.
Preparing to unpack .../094-apport_2.20.11-0ubuntu27.31_all.deb ...
Unpacking apport (2.20.11-0ubuntu27.31) ...
Selecting previously unselected package apport-symptoms.
Preparing to unpack .../095-apport-symptoms_0.23_all.deb ...
Unpacking apport-symptoms (0.23) ...
Selecting previously unselected package libaspell15:amd64.
Preparing to unpack .../096-libaspell15_0.60.8-1ubuntu0.1_amd64.deb ...
Unpacking libaspell15:amd64 (0.60.8-1ubuntu0.1) ...
Selecting previously unselected package emacsen-common.
Preparing to unpack .../097-emacsen-common_3.0.4_all.deb ...
Unpacking emacsen-common (3.0.4) ...
Selecting previously unselected package dictionaries-common.
Preparing to unpack .../098-dictionaries-common_1.28.1_all.deb ...
Adding 'diversion of /usr/share/dict/words to /usr/share/dict/words.pre-dictionaries-common by dictionaries-common'
Unpacking dictionaries-common (1.28.1) ...
Selecting previously unselected package aspell.
Preparing to unpack .../099-aspell_0.60.8-1ubuntu0.1_amd64.deb ...
Unpacking aspell (0.60.8-1ubuntu0.1) ...
Selecting previously unselected package aspell-en.
Preparing to unpack .../100-aspell-en_2018.04.16-0-1_all.deb ...
Unpacking aspell-en (2018.04.16-0-1) ...
Selecting previously unselected package libavahi-core7:amd64.
Preparing to unpack .../101-libavahi-core7_0.7-4ubuntu7.3_amd64.deb ...
Unpacking libavahi-core7:amd64 (0.7-4ubuntu7.3) ...
Selecting previously unselected package libdaemon0:amd64.
Preparing to unpack .../102-libdaemon0_0.14-7_amd64.deb ...
Unpacking libdaemon0:amd64 (0.14-7) ...
Selecting previously unselected package avahi-daemon.
Preparing to unpack .../103-avahi-daemon_0.7-4ubuntu7.3_amd64.deb ...
Unpacking avahi-daemon (0.7-4ubuntu7.3) ...
Selecting previously unselected package avahi-utils.
Preparing to unpack .../104-avahi-utils_0.7-4ubuntu7.3_amd64.deb ...
Unpacking avahi-utils (0.7-4ubuntu7.3) ...
Selecting previously unselected package bluez.
Preparing to unpack .../105-bluez_5.53-0ubuntu3.9_amd64.deb ...
Unpacking bluez (5.53-0ubuntu3.9) ...
Selecting previously unselected package bolt.
Preparing to unpack .../106-bolt_0.9.1-2~ubuntu20.04.2_amd64.deb ...
Unpacking bolt (0.9.1-2~ubuntu20.04.2) ...
Selecting previously unselected package bubblewrap.
Preparing to unpack .../107-bubblewrap_0.4.0-1ubuntu4.1_amd64.deb ...
Unpacking bubblewrap (0.4.0-1ubuntu4.1) ...
Selecting previously unselected package cheese-common.
Preparing to unpack .../108-cheese-common_3.34.0-1ubuntu1_all.deb ...
Unpacking cheese-common (3.34.0-1ubuntu1) ...
Selecting previously unselected package libgusb2:amd64.
Preparing to unpack .../109-libgusb2_0.3.4-0.1_amd64.deb ...
Unpacking libgusb2:amd64 (0.3.4-0.1) ...
Selecting previously unselected package libcolorhug2:amd64.
Preparing to unpack .../110-libcolorhug2_1.4.4-2_amd64.deb ...
Unpacking libcolorhug2:amd64 (1.4.4-2) ...
Selecting previously unselected package libexif12:amd64.
Preparing to unpack .../111-libexif12_0.6.21-6ubuntu0.4_amd64.deb ...
Unpacking libexif12:amd64 (0.6.21-6ubuntu0.4) ...
Selecting previously unselected package libxpm4:amd64.
Preparing to unpack .../112-libxpm4_1%3a3.5.12-1ubuntu0.20.04.2_amd64.deb ...
Unpacking libxpm4:amd64 (1:3.5.12-1ubuntu0.20.04.2) ...
Selecting previously unselected package libgd3:amd64.
Preparing to unpack .../113-libgd3_2.2.5-5.2ubuntu2.4_amd64.deb ...
Unpacking libgd3:amd64 (2.2.5-5.2ubuntu2.4) ...
Selecting previously unselected package libgphoto2-port12:amd64.
Preparing to unpack .../114-libgphoto2-port12_2.5.25-0ubuntu0.1_amd64.deb ...
Unpacking libgphoto2-port12:amd64 (2.5.25-0ubuntu0.1) ...
Selecting previously unselected package libgphoto2-6:amd64.
Preparing to unpack .../115-libgphoto2-6_2.5.25-0ubuntu0.1_amd64.deb ...
Unpacking libgphoto2-6:amd64 (2.5.25-0ubuntu0.1) ...
Selecting previously unselected package libieee1284-3:amd64.
Preparing to unpack .../116-libieee1284-3_0.2.11-13build1_amd64.deb ...
Unpacking libieee1284-3:amd64 (0.2.11-13build1) ...
Selecting previously unselected package mysql-common.
Preparing to unpack .../117-mysql-common_5.8+1.0.5ubuntu2_all.deb ...
Unpacking mysql-common (5.8+1.0.5ubuntu2) ...
Selecting previously unselected package libmysqlclient21:amd64.
Preparing to unpack .../118-libmysqlclient21_8.0.42-0ubuntu0.20.04.1_amd64.deb ...
Unpacking libmysqlclient21:amd64 (8.0.42-0ubuntu0.20.04.1) ...
Selecting previously unselected package libsnmp-base.
Preparing to unpack .../119-libsnmp-base_5.8+dfsg-2ubuntu2.9_all.deb ...
Unpacking libsnmp-base (5.8+dfsg-2ubuntu2.9) ...
Selecting previously unselected package libsnmp35:amd64.
Preparing to unpack .../120-libsnmp35_5.8+dfsg-2ubuntu2.9_amd64.deb ...
Unpacking libsnmp35:amd64 (5.8+dfsg-2ubuntu2.9) ...
Selecting previously unselected package libsane:amd64.
Preparing to unpack .../121-libsane_1.0.29-0ubuntu5.2_amd64.deb ...
Unpacking libsane:amd64 (1.0.29-0ubuntu5.2) ...
Selecting previously unselected package libpolkit-agent-1-0:amd64.
Preparing to unpack .../122-libpolkit-agent-1-0_0.105-26ubuntu1.3_amd64.deb ...
Unpacking libpolkit-agent-1-0:amd64 (0.105-26ubuntu1.3) ...
Selecting previously unselected package policykit-1.
Preparing to unpack .../123-policykit-1_0.105-26ubuntu1.3_amd64.deb ...
Unpacking policykit-1 (0.105-26ubuntu1.3) ...
Selecting previously unselected package colord-data.
Preparing to unpack .../124-colord-data_1.4.4-2_all.deb ...
Unpacking colord-data (1.4.4-2) ...
Selecting previously unselected package colord.
Preparing to unpack .../125-colord_1.4.4-2_amd64.deb ...
Unpacking colord (1.4.4-2) ...
Selecting previously unselected package libcrack2:amd64.
Preparing to unpack .../126-libcrack2_2.9.6-3.2_amd64.deb ...
Unpacking libcrack2:amd64 (2.9.6-3.2) ...
Selecting previously unselected package cracklib-runtime.
Preparing to unpack .../127-cracklib-runtime_2.9.6-3.2_amd64.deb ...
Unpacking cracklib-runtime (2.9.6-3.2) ...
Selecting previously unselected package libnl-genl-3-200:amd64.
Preparing to unpack .../128-libnl-genl-3-200_3.4.0-1ubuntu0.1_amd64.deb ...
Unpacking libnl-genl-3-200:amd64 (3.4.0-1ubuntu0.1) ...
Selecting previously unselected package wireless-regdb.
Preparing to unpack .../129-wireless-regdb_2024.07.04-0ubuntu1~20.04.1_all.deb ...
Unpacking wireless-regdb (2024.07.04-0ubuntu1~20.04.1) ...
Selecting previously unselected package iw.
Preparing to unpack .../130-iw_5.4-1_amd64.deb ...
Unpacking iw (5.4-1) ...
Selecting previously unselected package crda.
Preparing to unpack .../131-crda_3.18-1build1_amd64.deb ...
Unpacking crda (3.18-1build1) ...
Selecting previously unselected package dbus-x11.
Preparing to unpack .../132-dbus-x11_1.12.16-2ubuntu2.3_amd64.deb ...
Unpacking dbus-x11 (1.12.16-2ubuntu2.3) ...
Selecting previously unselected package dconf-cli.
Preparing to unpack .../133-dconf-cli_0.36.0-1_amd64.deb ...
Unpacking dconf-cli (0.36.0-1) ...
Selecting previously unselected package desktop-file-utils.
Preparing to unpack .../134-desktop-file-utils_0.24-1ubuntu3_amd64.deb ...
Unpacking desktop-file-utils (0.24-1ubuntu3) ...
Selecting previously unselected package dns-root-data.
Preparing to unpack .../135-dns-root-data_2024071801~ubuntu0.20.04.1_all.deb ...
Unpacking dns-root-data (2024071801~ubuntu0.20.04.1) ...
Selecting previously unselected package dnsmasq-base.
Preparing to unpack .../136-dnsmasq-base_2.90-0ubuntu0.20.04.1_amd64.deb ...
Unpacking dnsmasq-base (2.90-0ubuntu0.20.04.1) ...
Selecting previously unselected package xml-core.
Preparing to unpack .../137-xml-core_0.18+nmu1_all.deb ...
Unpacking xml-core (0.18+nmu1) ...
Selecting previously unselected package sgml-data.
Preparing to unpack .../138-sgml-data_2.0.11_all.deb ...
Unpacking sgml-data (2.0.11) ...
Selecting previously unselected package docbook-xml.
Preparing to unpack .../139-docbook-xml_4.5-9_all.deb ...
Unpacking docbook-xml (4.5-9) ...
Selecting previously unselected package hunspell-en-us.
Preparing to unpack .../140-hunspell-en-us_1%3a2018.04.16-1_all.deb ...
Unpacking hunspell-en-us (1:2018.04.16-1) ...
Selecting previously unselected package libhunspell-1.7-0:amd64.
Preparing to unpack .../141-libhunspell-1.7-0_1.7.0-2build2_amd64.deb ...
Unpacking libhunspell-1.7-0:amd64 (1.7.0-2build2) ...
Selecting previously unselected package libenchant-2-2:amd64.
Preparing to unpack .../142-libenchant-2-2_2.2.8-1ubuntu0.20.04.1_amd64.deb ...
Unpacking libenchant-2-2:amd64 (2.2.8-1ubuntu0.20.04.1) ...
Selecting previously unselected package enchant-2.
Preparing to unpack .../143-enchant-2_2.2.8-1ubuntu0.20.04.1_amd64.deb ...
Unpacking enchant-2 (2.2.8-1ubuntu0.20.04.1) ...
Selecting previously unselected package libcamel-1.2-62:amd64.
Preparing to unpack .../144-libcamel-1.2-62_3.36.5-0ubuntu1_amd64.deb ...
Unpacking libcamel-1.2-62:amd64 (3.36.5-0ubuntu1) ...
Selecting previously unselected package libcanberra-gtk3-0:amd64.
Preparing to unpack .../145-libcanberra-gtk3-0_0.30-7ubuntu1_amd64.deb ...
Unpacking libcanberra-gtk3-0:amd64 (0.30-7ubuntu1) ...
Selecting previously unselected package libgck-1-0:amd64.
Preparing to unpack .../146-libgck-1-0_3.36.0-2build1_amd64.deb ...
Unpacking libgck-1-0:amd64 (3.36.0-2build1) ...
Selecting previously unselected package libgcr-base-3-1:amd64.
Preparing to unpack .../147-libgcr-base-3-1_3.36.0-2build1_amd64.deb ...
Unpacking libgcr-base-3-1:amd64 (3.36.0-2build1) ...
Selecting previously unselected package libgoa-1.0-common.
Preparing to unpack .../148-libgoa-1.0-common_3.36.1-0ubuntu1_all.deb ...
Unpacking libgoa-1.0-common (3.36.1-0ubuntu1) ...
Selecting previously unselected package libgoa-1.0-0b:amd64.
Preparing to unpack .../149-libgoa-1.0-0b_3.36.1-0ubuntu1_amd64.deb ...
Unpacking libgoa-1.0-0b:amd64 (3.36.1-0ubuntu1) ...
Selecting previously unselected package libgdata-common.
Preparing to unpack .../150-libgdata-common_0.17.12-1_all.deb ...
Unpacking libgdata-common (0.17.12-1) ...
Selecting previously unselected package libgdata22:amd64.
Preparing to unpack .../151-libgdata22_0.17.12-1_amd64.deb ...
Unpacking libgdata22:amd64 (0.17.12-1) ...
Selecting previously unselected package libsecret-common.
Preparing to unpack .../152-libsecret-common_0.20.4-0ubuntu1_all.deb ...
Unpacking libsecret-common (0.20.4-0ubuntu1) ...
Selecting previously unselected package libsecret-1-0:amd64.
Preparing to unpack .../153-libsecret-1-0_0.20.4-0ubuntu1_amd64.deb ...
Unpacking libsecret-1-0:amd64 (0.20.4-0ubuntu1) ...
Selecting previously unselected package evolution-data-server-common.
Preparing to unpack .../154-evolution-data-server-common_3.36.5-0ubuntu1_all.deb ...
Unpacking evolution-data-server-common (3.36.5-0ubuntu1) ...
Selecting previously unselected package libedataserver-1.2-24:amd64.
Preparing to unpack .../155-libedataserver-1.2-24_3.36.5-0ubuntu1_amd64.deb ...
Unpacking libedataserver-1.2-24:amd64 (3.36.5-0ubuntu1) ...
Selecting previously unselected package libebackend-1.2-10:amd64.
Preparing to unpack .../156-libebackend-1.2-10_3.36.5-0ubuntu1_amd64.deb ...
Unpacking libebackend-1.2-10:amd64 (3.36.5-0ubuntu1) ...
Selecting previously unselected package libprotobuf17:amd64.
Preparing to unpack .../157-libprotobuf17_3.6.1.3-2ubuntu5.2_amd64.deb ...
Unpacking libprotobuf17:amd64 (3.6.1.3-2ubuntu5.2) ...
Selecting previously unselected package libphonenumber7:amd64.
Preparing to unpack .../158-libphonenumber7_7.1.0-5ubuntu11_amd64.deb ...
Unpacking libphonenumber7:amd64 (7.1.0-5ubuntu11) ...
Selecting previously unselected package libebook-contacts-1.2-3:amd64.
Preparing to unpack .../159-libebook-contacts-1.2-3_3.36.5-0ubuntu1_amd64.deb ...
Unpacking libebook-contacts-1.2-3:amd64 (3.36.5-0ubuntu1) ...
Selecting previously unselected package libedata-book-1.2-26:amd64.
Preparing to unpack .../160-libedata-book-1.2-26_3.36.5-0ubuntu1_amd64.deb ...
Unpacking libedata-book-1.2-26:amd64 (3.36.5-0ubuntu1) ...
Selecting previously unselected package libebook-1.2-20:amd64.
Preparing to unpack .../161-libebook-1.2-20_3.36.5-0ubuntu1_amd64.deb ...
Unpacking libebook-1.2-20:amd64 (3.36.5-0ubuntu1) ...
Selecting previously unselected package libical3:amd64.
Preparing to unpack .../162-libical3_3.0.8-1_amd64.deb ...
Unpacking libical3:amd64 (3.0.8-1) ...
Selecting previously unselected package libecal-2.0-1:amd64.
Preparing to unpack .../163-libecal-2.0-1_3.36.5-0ubuntu1_amd64.deb ...
Unpacking libecal-2.0-1:amd64 (3.36.5-0ubuntu1) ...
Selecting previously unselected package libedata-cal-2.0-1:amd64.
Preparing to unpack .../164-libedata-cal-2.0-1_3.36.5-0ubuntu1_amd64.deb ...
Unpacking libedata-cal-2.0-1:amd64 (3.36.5-0ubuntu1) ...
Selecting previously unselected package libgcr-ui-3-1:amd64.
Preparing to unpack .../165-libgcr-ui-3-1_3.36.0-2build1_amd64.deb ...
Unpacking libgcr-ui-3-1:amd64 (3.36.0-2build1) ...
Selecting previously unselected package libjavascriptcoregtk-4.0-18:amd64.
Preparing to unpack .../166-libjavascriptcoregtk-4.0-18_2.38.6-0ubuntu0.20.04.1_amd64.deb ...
Unpacking libjavascriptcoregtk-4.0-18:amd64 (2.38.6-0ubuntu0.20.04.1) ...
Selecting previously unselected package xdg-dbus-proxy.
Preparing to unpack .../167-xdg-dbus-proxy_0.1.2-1_amd64.deb ...
Unpacking xdg-dbus-proxy (0.1.2-1) ...
Selecting previously unselected package libgstreamer-gl1.0-0:amd64.
Preparing to unpack .../168-libgstreamer-gl1.0-0_1.16.3-0ubuntu1.4_amd64.deb ...
Unpacking libgstreamer-gl1.0-0:amd64 (1.16.3-0ubuntu1.4) ...
Selecting previously unselected package libhyphen0:amd64.
Preparing to unpack .../169-libhyphen0_2.8.8-7_amd64.deb ...
Unpacking libhyphen0:amd64 (2.8.8-7) ...
Selecting previously unselected package libwoff1:amd64.
Preparing to unpack .../170-libwoff1_1.0.2-1build2_amd64.deb ...
Unpacking libwoff1:amd64 (1.0.2-1build2) ...
Selecting previously unselected package libxslt1.1:amd64.
Preparing to unpack .../171-libxslt1.1_1.1.34-4ubuntu0.20.04.3_amd64.deb ...
Unpacking libxslt1.1:amd64 (1.1.34-4ubuntu0.20.04.3) ...
Selecting previously unselected package libwebkit2gtk-4.0-37:amd64.
Preparing to unpack .../172-libwebkit2gtk-4.0-37_2.38.6-0ubuntu0.20.04.1_amd64.deb ...
Unpacking libwebkit2gtk-4.0-37:amd64 (2.38.6-0ubuntu0.20.04.1) ...
Selecting previously unselected package libedataserverui-1.2-2:amd64.
Preparing to unpack .../173-libedataserverui-1.2-2_3.36.5-0ubuntu1_amd64.deb ...
Unpacking libedataserverui-1.2-2:amd64 (3.36.5-0ubuntu1) ...
Selecting previously unselected package libgeocode-glib0:amd64.
Preparing to unpack .../174-libgeocode-glib0_3.26.2-2_amd64.deb ...
Unpacking libgeocode-glib0:amd64 (3.26.2-2) ...
Selecting previously unselected package libgweather-common.
Preparing to unpack .../175-libgweather-common_3.36.1-1~ubuntu20.04.1_all.deb ...
Unpacking libgweather-common (3.36.1-1~ubuntu20.04.1) ...
Selecting previously unselected package libgweather-3-16:amd64.
Preparing to unpack .../176-libgweather-3-16_3.36.1-1~ubuntu20.04.1_amd64.deb ...
Unpacking libgweather-3-16:amd64 (3.36.1-1~ubuntu20.04.1) ...
Selecting previously unselected package gcr.
Preparing to unpack .../177-gcr_3.36.0-2build1_amd64.deb ...
Unpacking gcr (3.36.0-2build1) ...
Selecting previously unselected package p11-kit-modules:amd64.
Preparing to unpack .../178-p11-kit-modules_0.23.20-1ubuntu0.1_amd64.deb ...
Unpacking p11-kit-modules:amd64 (0.23.20-1ubuntu0.1) ...
Selecting previously unselected package p11-kit.
Preparing to unpack .../179-p11-kit_0.23.20-1ubuntu0.1_amd64.deb ...
Unpacking p11-kit (0.23.20-1ubuntu0.1) ...
Selecting previously unselected package pinentry-gnome3.
Preparing to unpack .../180-pinentry-gnome3_1.1.0-3build1_amd64.deb ...
Unpacking pinentry-gnome3 (1.1.0-3build1) ...
Selecting previously unselected package gnome-keyring.
Preparing to unpack .../181-gnome-keyring_3.36.0-1ubuntu1_amd64.deb ...
Unpacking gnome-keyring (3.36.0-1ubuntu1) ...
Selecting previously unselected package evolution-data-server.
Preparing to unpack .../182-evolution-data-server_3.36.5-0ubuntu1_amd64.deb ...
Unpacking evolution-data-server (3.36.5-0ubuntu1) ...
Selecting previously unselected package libgdm1.
Preparing to unpack .../183-libgdm1_3.36.3-0ubuntu0.20.04.4_amd64.deb ...
Unpacking libgdm1 (3.36.3-0ubuntu0.20.04.4) ...
Selecting previously unselected package gir1.2-gdm-1.0:amd64.
Preparing to unpack .../184-gir1.2-gdm-1.0_3.36.3-0ubuntu0.20.04.4_amd64.deb ...
Unpacking gir1.2-gdm-1.0:amd64 (3.36.3-0ubuntu0.20.04.4) ...
Selecting previously unselected package libplist3:amd64.
Preparing to unpack .../185-libplist3_2.1.0-4build2_amd64.deb ...
Unpacking libplist3:amd64 (2.1.0-4build2) ...
Selecting previously unselected package libusbmuxd6:amd64.
Preparing to unpack .../186-libusbmuxd6_2.0.1-2_amd64.deb ...
Unpacking libusbmuxd6:amd64 (2.0.1-2) ...
Selecting previously unselected package libimobiledevice6:amd64.
Preparing to unpack .../187-libimobiledevice6_1.2.1~git20191129.9f79242-1build1_amd64.deb ...
Unpacking libimobiledevice6:amd64 (1.2.1~git20191129.9f79242-1build1) ...
Selecting previously unselected package libupower-glib3:amd64.
Preparing to unpack .../188-libupower-glib3_0.99.11-1build2_amd64.deb ...
Unpacking libupower-glib3:amd64 (0.99.11-1build2) ...
Selecting previously unselected package upower.
Preparing to unpack .../189-upower_0.99.11-1build2_amd64.deb ...
Unpacking upower (0.99.11-1build2) ...
Selecting previously unselected package gnome-desktop3-data.
Preparing to unpack .../190-gnome-desktop3-data_3.36.8-0ubuntu1_all.deb ...
Unpacking gnome-desktop3-data (3.36.8-0ubuntu1) ...
Selecting previously unselected package libgnome-desktop-3-19:amd64.
Preparing to unpack .../191-libgnome-desktop-3-19_3.36.8-0ubuntu1_amd64.deb ...
Unpacking libgnome-desktop-3-19:amd64 (3.36.8-0ubuntu1) ...
Selecting previously unselected package gnome-session-bin.
Preparing to unpack .../192-gnome-session-bin_3.36.0-2ubuntu1_amd64.deb ...
Unpacking gnome-session-bin (3.36.0-2ubuntu1) ...
Selecting previously unselected package gnome-session-common.
Preparing to unpack .../193-gnome-session-common_3.36.0-2ubuntu1_all.deb ...
Unpacking gnome-session-common (3.36.0-2ubuntu1) ...
Selecting previously unselected package gnome-settings-daemon-common.
Preparing to unpack .../194-gnome-settings-daemon-common_3.36.1-0ubuntu1.1_all.deb ...
Unpacking gnome-settings-daemon-common (3.36.1-0ubuntu1.1) ...
Selecting previously unselected package libxt6:amd64.
Preparing to unpack .../195-libxt6_1%3a1.1.5-1_amd64.deb ...
Unpacking libxt6:amd64 (1:1.1.5-1) ...
Selecting previously unselected package libxmu6:amd64.
Preparing to unpack .../196-libxmu6_2%3a1.1.3-0ubuntu1_amd64.deb ...
Unpacking libxmu6:amd64 (2:1.1.3-0ubuntu1) ...
Selecting previously unselected package libxaw7:amd64.
Preparing to unpack .../197-libxaw7_2%3a1.0.13-1_amd64.deb ...
Unpacking libxaw7:amd64 (2:1.0.13-1) ...
Selecting previously unselected package x11-xserver-utils.
Preparing to unpack .../198-x11-xserver-utils_7.7+8_amd64.deb ...
Unpacking x11-xserver-utils (7.7+8) ...
Selecting previously unselected package libmm-glib0:amd64.
Preparing to unpack .../199-libmm-glib0_1.18.6-1~ubuntu20.04.1_amd64.deb ...
Unpacking libmm-glib0:amd64 (1.18.6-1~ubuntu20.04.1) ...
Selecting previously unselected package libnotify4:amd64.
Preparing to unpack .../200-libnotify4_0.7.9-1ubuntu3.20.04.2_amd64.deb ...
Unpacking libnotify4:amd64 (0.7.9-1ubuntu3.20.04.2) ...
Selecting previously unselected package geoclue-2.0.
Preparing to unpack .../201-geoclue-2.0_2.5.6-0ubuntu1_amd64.deb ...
Unpacking geoclue-2.0 (2.5.6-0ubuntu1) ...
Selecting previously unselected package libgeoclue-2-0:amd64.
Preparing to unpack .../202-libgeoclue-2-0_2.5.6-0ubuntu1_amd64.deb ...
Unpacking libgeoclue-2-0:amd64 (2.5.6-0ubuntu1) ...
Selecting previously unselected package libnm0:amd64.
Preparing to unpack .../203-libnm0_1.22.10-1ubuntu2.4_amd64.deb ...
Unpacking libnm0:amd64 (1.22.10-1ubuntu2.4) ...
Selecting previously unselected package libpulse-mainloop-glib0:amd64.
Preparing to unpack .../204-libpulse-mainloop-glib0_1%3a13.99.1-1ubuntu3.13_amd64.deb ...
Unpacking libpulse-mainloop-glib0:amd64 (1:13.99.1-1ubuntu3.13) ...
Selecting previously unselected package gnome-settings-daemon.
Preparing to unpack .../205-gnome-settings-daemon_3.36.1-0ubuntu1.1_amd64.deb ...
Unpacking gnome-settings-daemon (3.36.1-0ubuntu1.1) ...
Selecting previously unselected package gir1.2-accountsservice-1.0.
Preparing to unpack .../206-gir1.2-accountsservice-1.0_0.6.55-0ubuntu12~20.04.7_amd64.deb ...
Unpacking gir1.2-accountsservice-1.0 (0.6.55-0ubuntu12~20.04.7) ...
Selecting previously unselected package gir1.2-gck-1:amd64.
Preparing to unpack .../207-gir1.2-gck-1_3.36.0-2build1_amd64.deb ...
Unpacking gir1.2-gck-1:amd64 (3.36.0-2build1) ...
Selecting previously unselected package gir1.2-gcr-3:amd64.
Preparing to unpack .../208-gir1.2-gcr-3_3.36.0-2build1_amd64.deb ...
Unpacking gir1.2-gcr-3:amd64 (3.36.0-2build1) ...
Selecting previously unselected package gir1.2-gdesktopenums-3.0:amd64.
Preparing to unpack .../209-gir1.2-gdesktopenums-3.0_3.36.0-1ubuntu1_amd64.deb ...
Unpacking gir1.2-gdesktopenums-3.0:amd64 (3.36.0-1ubuntu1) ...
Selecting previously unselected package gir1.2-geoclue-2.0:amd64.
Preparing to unpack .../210-gir1.2-geoclue-2.0_2.5.6-0ubuntu1_amd64.deb ...
Unpacking gir1.2-geoclue-2.0:amd64 (2.5.6-0ubuntu1) ...
Selecting previously unselected package libgnome-bluetooth13:amd64.
Preparing to unpack .../211-libgnome-bluetooth13_3.34.3-0ubuntu1_amd64.deb ...
Unpacking libgnome-bluetooth13:amd64 (3.34.3-0ubuntu1) ...
Selecting previously unselected package gir1.2-gnomebluetooth-1.0:amd64.
Preparing to unpack .../212-gir1.2-gnomebluetooth-1.0_3.34.3-0ubuntu1_amd64.deb ...
Unpacking gir1.2-gnomebluetooth-1.0:amd64 (3.34.3-0ubuntu1) ...
Selecting previously unselected package gir1.2-gnomedesktop-3.0:amd64.
Preparing to unpack .../213-gir1.2-gnomedesktop-3.0_3.36.8-0ubuntu1_amd64.deb ...
Unpacking gir1.2-gnomedesktop-3.0:amd64 (3.36.8-0ubuntu1) ...
Selecting previously unselected package gir1.2-gweather-3.0:amd64.
Preparing to unpack .../214-gir1.2-gweather-3.0_3.36.1-1~ubuntu20.04.1_amd64.deb ...
Unpacking gir1.2-gweather-3.0:amd64 (3.36.1-1~ubuntu20.04.1) ...
Selecting previously unselected package libibus-1.0-5:amd64.
Preparing to unpack .../215-libibus-1.0-5_1.5.22-2ubuntu2.1_amd64.deb ...
Unpacking libibus-1.0-5:amd64 (1.5.22-2ubuntu2.1) ...
Selecting previously unselected package gir1.2-ibus-1.0:amd64.
Preparing to unpack .../216-gir1.2-ibus-1.0_1.5.22-2ubuntu2.1_amd64.deb ...
Unpacking gir1.2-ibus-1.0:amd64 (1.5.22-2ubuntu2.1) ...
Selecting previously unselected package mutter-common.
Preparing to unpack .../217-mutter-common_3.36.9-0ubuntu0.20.04.2_all.deb ...
Unpacking mutter-common (3.36.9-0ubuntu0.20.04.2) ...
Selecting previously unselected package libgraphene-1.0-0:amd64.
Preparing to unpack .../218-libgraphene-1.0-0_1.10.0-1build2_amd64.deb ...
Unpacking libgraphene-1.0-0:amd64 (1.10.0-1build2) ...
Selecting previously unselected package libstartup-notification0:amd64.
Preparing to unpack .../219-libstartup-notification0_0.12-6_amd64.deb ...
Unpacking libstartup-notification0:amd64 (0.12-6) ...
Selecting previously unselected package libxcb-res0:amd64.
Preparing to unpack .../220-libxcb-res0_1.14-2_amd64.deb ...
Unpacking libxcb-res0:amd64 (1.14-2) ...
Selecting previously unselected package libxkbfile1:amd64.
Preparing to unpack .../221-libxkbfile1_1%3a1.1.0-1_amd64.deb ...
Unpacking libxkbfile1:amd64 (1:1.1.0-1) ...
Selecting previously unselected package libmutter-6-0:amd64.
Preparing to unpack .../222-libmutter-6-0_3.36.9-0ubuntu0.20.04.2_amd64.deb ...
Unpacking libmutter-6-0:amd64 (3.36.9-0ubuntu0.20.04.2) ...
Selecting previously unselected package gir1.2-graphene-1.0:amd64.
Preparing to unpack .../223-gir1.2-graphene-1.0_1.10.0-1build2_amd64.deb ...
Unpacking gir1.2-graphene-1.0:amd64 (1.10.0-1build2) ...
Selecting previously unselected package gir1.2-json-1.0:amd64.
Preparing to unpack .../224-gir1.2-json-1.0_1.4.4-2ubuntu2_amd64.deb ...
Unpacking gir1.2-json-1.0:amd64 (1.4.4-2ubuntu2) ...
Selecting previously unselected package gir1.2-mutter-6:amd64.
Preparing to unpack .../225-gir1.2-mutter-6_3.36.9-0ubuntu0.20.04.2_amd64.deb ...
Unpacking gir1.2-mutter-6:amd64 (3.36.9-0ubuntu0.20.04.2) ...
Selecting previously unselected package gir1.2-nm-1.0:amd64.
Preparing to unpack .../226-gir1.2-nm-1.0_1.22.10-1ubuntu2.4_amd64.deb ...
Unpacking gir1.2-nm-1.0:amd64 (1.22.10-1ubuntu2.4) ...
Selecting previously unselected package libnma0:amd64.
Preparing to unpack .../227-libnma0_1.8.24-1ubuntu3_amd64.deb ...
Unpacking libnma0:amd64 (1.8.24-1ubuntu3) ...
Selecting previously unselected package gir1.2-nma-1.0:amd64.
Preparing to unpack .../228-gir1.2-nma-1.0_1.8.24-1ubuntu3_amd64.deb ...
Unpacking gir1.2-nma-1.0:amd64 (1.8.24-1ubuntu3) ...
Selecting previously unselected package gir1.2-polkit-1.0.
Preparing to unpack .../229-gir1.2-polkit-1.0_0.105-26ubuntu1.3_amd64.deb ...
Unpacking gir1.2-polkit-1.0 (0.105-26ubuntu1.3) ...
Selecting previously unselected package gir1.2-rsvg-2.0:amd64.
Preparing to unpack .../230-gir1.2-rsvg-2.0_2.48.9-1ubuntu0.20.04.4_amd64.deb ...
Unpacking gir1.2-rsvg-2.0:amd64 (2.48.9-1ubuntu0.20.04.4) ...
Selecting previously unselected package gir1.2-soup-2.4:amd64.
Preparing to unpack .../231-gir1.2-soup-2.4_2.70.0-1ubuntu0.5_amd64.deb ...
Unpacking gir1.2-soup-2.4:amd64 (2.70.0-1ubuntu0.5) ...
Selecting previously unselected package gir1.2-upowerglib-1.0:amd64.
Preparing to unpack .../232-gir1.2-upowerglib-1.0_0.99.11-1build2_amd64.deb ...
Unpacking gir1.2-upowerglib-1.0:amd64 (0.99.11-1build2) ...
Selecting previously unselected package libmozjs-68-0:amd64.
Preparing to unpack .../233-libmozjs-68-0_68.6.0-1ubuntu1_amd64.deb ...
Unpacking libmozjs-68-0:amd64 (68.6.0-1ubuntu1) ...
Selecting previously unselected package libgjs0g:amd64.
Preparing to unpack .../234-libgjs0g_1.64.5-0ubuntu0.20.04.01_amd64.deb ...
Unpacking libgjs0g:amd64 (1.64.5-0ubuntu0.20.04.01) ...
Selecting previously unselected package gjs.
Preparing to unpack .../235-gjs_1.64.5-0ubuntu0.20.04.01_amd64.deb ...
Unpacking gjs (1.64.5-0ubuntu0.20.04.01) ...
Selecting previously unselected package gnome-shell-common.
Preparing to unpack .../236-gnome-shell-common_3.36.9-0ubuntu0.20.04.5_all.deb ...
Unpacking gnome-shell-common (3.36.9-0ubuntu0.20.04.5) ...
Selecting previously unselected package ubuntu-wallpapers-focal.
Preparing to unpack .../237-ubuntu-wallpapers-focal_20.04.2-0ubuntu1_all.deb ...
Unpacking ubuntu-wallpapers-focal (20.04.2-0ubuntu1) ...
Selecting previously unselected package ubuntu-wallpapers.
Preparing to unpack .../238-ubuntu-wallpapers_20.04.2-0ubuntu1_all.deb ...
Unpacking ubuntu-wallpapers (20.04.2-0ubuntu1) ...
Selecting previously unselected package zenity-common.
Preparing to unpack .../239-zenity-common_3.32.0-5_all.deb ...
Unpacking zenity-common (3.32.0-5) ...
Selecting previously unselected package zenity.
Preparing to unpack .../240-zenity_3.32.0-5_amd64.deb ...
Unpacking zenity (3.32.0-5) ...
Selecting previously unselected package mutter.
Preparing to unpack .../241-mutter_3.36.9-0ubuntu0.20.04.2_amd64.deb ...
Unpacking mutter (3.36.9-0ubuntu0.20.04.2) ...
Selecting previously unselected package libgnome-autoar-0-0:amd64.
Preparing to unpack .../242-libgnome-autoar-0-0_0.2.3-2ubuntu0.4_amd64.deb ...
Unpacking libgnome-autoar-0-0:amd64 (0.2.3-2ubuntu0.4) ...
Selecting previously unselected package gnome-shell.
Preparing to unpack .../243-gnome-shell_3.36.9-0ubuntu0.20.04.5_amd64.deb ...
Unpacking gnome-shell (3.36.9-0ubuntu0.20.04.5) ...
Selecting previously unselected package x11-xkb-utils.
Preparing to unpack .../244-x11-xkb-utils_7.7+5_amd64.deb ...
Unpacking x11-xkb-utils (7.7+5) ...
Selecting previously unselected package xserver-common.
Preparing to unpack .../245-xserver-common_2%3a1.20.13-1ubuntu1~20.04.20_all.deb ...
Unpacking xserver-common (2:1.20.13-1ubuntu1~20.04.20) ...
Selecting previously unselected package libunwind8:amd64.
Preparing to unpack .../246-libunwind8_1.2.1-9ubuntu0.1_amd64.deb ...
Unpacking libunwind8:amd64 (1.2.1-9ubuntu0.1) ...
Selecting previously unselected package libfontenc1:amd64.
Preparing to unpack .../247-libfontenc1_1%3a1.1.4-0ubuntu1_amd64.deb ...
Unpacking libfontenc1:amd64 (1:1.1.4-0ubuntu1) ...
Selecting previously unselected package libxfont2:amd64.
Preparing to unpack .../248-libxfont2_1%3a2.0.3-1_amd64.deb ...
Unpacking libxfont2:amd64 (1:2.0.3-1) ...
Selecting previously unselected package xwayland.
Preparing to unpack .../249-xwayland_2%3a1.20.13-1ubuntu1~20.04.20_amd64.deb ...
Unpacking xwayland (2:1.20.13-1ubuntu1~20.04.20) ...
Selecting previously unselected package yaru-theme-gnome-shell.
Preparing to unpack .../250-yaru-theme-gnome-shell_**********_all.deb ...
Unpacking yaru-theme-gnome-shell (**********) ...
Selecting previously unselected package session-migration.
Preparing to unpack .../251-session-migration_0.3.5_amd64.deb ...
Unpacking session-migration (0.3.5) ...
Selecting previously unselected package ubuntu-session.
Preparing to unpack .../252-ubuntu-session_3.36.0-2ubuntu1_all.deb ...
Unpacking ubuntu-session (3.36.0-2ubuntu1) ...
Selecting previously unselected package gdm3.
Preparing to unpack .../253-gdm3_3.36.3-0ubuntu0.20.04.4_amd64.deb ...
Unpacking gdm3 (3.36.3-0ubuntu0.20.04.4) ...
Selecting previously unselected package gir1.2-notify-0.7:amd64.
Preparing to unpack .../254-gir1.2-notify-0.7_0.7.9-1ubuntu3.20.04.2_amd64.deb ...
Unpacking gir1.2-notify-0.7:amd64 (0.7.9-1ubuntu3.20.04.2) ...
Selecting previously unselected package libpackagekit-glib2-18:amd64.
Preparing to unpack .../255-libpackagekit-glib2-18_1.1.13-2ubuntu1.1_amd64.deb ...
Unpacking libpackagekit-glib2-18:amd64 (1.1.13-2ubuntu1.1) ...
Selecting previously unselected package gir1.2-packagekitglib-1.0.
Preparing to unpack .../256-gir1.2-packagekitglib-1.0_1.1.13-2ubuntu1.1_amd64.deb ...
Unpacking gir1.2-packagekitglib-1.0 (1.1.13-2ubuntu1.1) ...
Selecting previously unselected package gir1.2-secret-1:amd64.
Preparing to unpack .../257-gir1.2-secret-1_0.20.4-0ubuntu1_amd64.deb ...
Unpacking gir1.2-secret-1:amd64 (0.20.4-0ubuntu1) ...
Selecting previously unselected package gir1.2-vte-2.91:amd64.
Preparing to unpack .../258-gir1.2-vte-2.91_0.60.3-0ubuntu1~20.5_amd64.deb ...
Unpacking gir1.2-vte-2.91:amd64 (0.60.3-0ubuntu1~20.5) ...
Selecting previously unselected package libgnomekbd-common.
Preparing to unpack .../259-libgnomekbd-common_3.26.1-1_all.deb ...
Unpacking libgnomekbd-common (3.26.1-1) ...
Selecting previously unselected package libxklavier16:amd64.
Preparing to unpack .../260-libxklavier16_5.4-4_amd64.deb ...
Unpacking libxklavier16:amd64 (5.4-4) ...
Selecting previously unselected package libgnomekbd8:amd64.
Preparing to unpack .../261-libgnomekbd8_3.26.1-1_amd64.deb ...
Unpacking libgnomekbd8:amd64 (3.26.1-1) ...
Selecting previously unselected package gkbd-capplet.
Preparing to unpack .../262-gkbd-capplet_3.26.1-1_amd64.deb ...
Unpacking gkbd-capplet (3.26.1-1) ...
Selecting previously unselected package libcogl20:amd64.
Preparing to unpack .../263-libcogl20_1.22.6-1_amd64.deb ...
Unpacking libcogl20:amd64 (1.22.6-1) ...
Selecting previously unselected package libcogl-pango20:amd64.
Preparing to unpack .../264-libcogl-pango20_1.22.6-1_amd64.deb ...
Unpacking libcogl-pango20:amd64 (1.22.6-1) ...
Selecting previously unselected package libcogl-path20:amd64.
Preparing to unpack .../265-libcogl-path20_1.22.6-1_amd64.deb ...
Unpacking libcogl-path20:amd64 (1.22.6-1) ...
Selecting previously unselected package libclutter-1.0-0:amd64.
Preparing to unpack .../266-libclutter-1.0-0_1.26.4+dfsg-1_amd64.deb ...
Unpacking libclutter-1.0-0:amd64 (1.26.4+dfsg-1) ...
Selecting previously unselected package libclutter-gst-3.0-0:amd64.
Preparing to unpack .../267-libclutter-gst-3.0-0_3.0.27-1_amd64.deb ...
Unpacking libclutter-gst-3.0-0:amd64 (3.0.27-1) ...
Selecting previously unselected package libcheese8:amd64.
Preparing to unpack .../268-libcheese8_3.34.0-1ubuntu1_amd64.deb ...
Unpacking libcheese8:amd64 (3.34.0-1ubuntu1) ...
Selecting previously unselected package libclutter-gtk-1.0-0:amd64.
Preparing to unpack .../269-libclutter-gtk-1.0-0_1.8.4-4_amd64.deb ...
Unpacking libclutter-gtk-1.0-0:amd64 (1.8.4-4) ...
Selecting previously unselected package gstreamer1.0-clutter-3.0:amd64.
Preparing to unpack .../270-gstreamer1.0-clutter-3.0_3.0.27-1_amd64.deb ...
Unpacking gstreamer1.0-clutter-3.0:amd64 (3.0.27-1) ...
Selecting previously unselected package libcheese-gtk25:amd64.
Preparing to unpack .../271-libcheese-gtk25_3.34.0-1ubuntu1_amd64.deb ...
Unpacking libcheese-gtk25:amd64 (3.34.0-1ubuntu1) ...
Selecting previously unselected package libcolord-gtk1:amd64.
Preparing to unpack .../272-libcolord-gtk1_0.2.0-0ubuntu1_amd64.deb ...
Unpacking libcolord-gtk1:amd64 (0.2.0-0ubuntu1) ...
Selecting previously unselected package libgoa-backend-1.0-1:amd64.
Preparing to unpack .../273-libgoa-backend-1.0-1_3.36.1-0ubuntu1_amd64.deb ...
Unpacking libgoa-backend-1.0-1:amd64 (3.36.1-0ubuntu1) ...
Selecting previously unselected package libgsound0:amd64.
Preparing to unpack .../274-libgsound0_1.0.2-4_amd64.deb ...
Unpacking libgsound0:amd64 (1.0.2-4) ...
Selecting previously unselected package libgtop2-common.
Preparing to unpack .../275-libgtop2-common_2.40.0-2_all.deb ...
Unpacking libgtop2-common (2.40.0-2) ...
Selecting previously unselected package libgtop-2.0-11:amd64.
Preparing to unpack .../276-libgtop-2.0-11_2.40.0-2_amd64.deb ...
Unpacking libgtop-2.0-11:amd64 (2.40.0-2) ...
Selecting previously unselected package libpwquality-common.
Preparing to unpack .../277-libpwquality-common_1.4.2-1build1_all.deb ...
Unpacking libpwquality-common (1.4.2-1build1) ...
Selecting previously unselected package libpwquality1:amd64.
Preparing to unpack .../278-libpwquality1_1.4.2-1build1_amd64.deb ...
Unpacking libpwquality1:amd64 (1.4.2-1build1) ...
Selecting previously unselected package libudisks2-0:amd64.
Preparing to unpack .../279-libudisks2-0_2.8.4-1ubuntu2_amd64.deb ...
Unpacking libudisks2-0:amd64 (2.8.4-1ubuntu2) ...
Selecting previously unselected package libwhoopsie-preferences0.
Preparing to unpack .../280-libwhoopsie-preferences0_22_amd64.deb ...
Unpacking libwhoopsie-preferences0 (22) ...
Selecting previously unselected package gnome-control-center-data.
Preparing to unpack .../281-gnome-control-center-data_1%3a3.36.5-0ubuntu4.1_all.deb ...
Unpacking gnome-control-center-data (1:3.36.5-0ubuntu4.1) ...
Selecting previously unselected package python3-defer.
Preparing to unpack .../282-python3-defer_1.0.6-2.1_all.deb ...
Unpacking python3-defer (1.0.6-2.1) ...
Selecting previously unselected package python3-aptdaemon.
Preparing to unpack .../283-python3-aptdaemon_1.1.1+bzr982-0ubuntu32.3_all.deb ...
Unpacking python3-aptdaemon (1.1.1+bzr982-0ubuntu32.3) ...
Selecting previously unselected package aptdaemon.
Preparing to unpack .../284-aptdaemon_1.1.1+bzr982-0ubuntu32.3_all.deb ...
Unpacking aptdaemon (1.1.1+bzr982-0ubuntu32.3) ...
Selecting previously unselected package aptdaemon-data.
Preparing to unpack .../285-aptdaemon-data_1.1.1+bzr982-0ubuntu32.3_all.deb ...
Unpacking aptdaemon-data (1.1.1+bzr982-0ubuntu32.3) ...
Selecting previously unselected package python3-aptdaemon.gtk3widgets.
Preparing to unpack .../286-python3-aptdaemon.gtk3widgets_1.1.1+bzr982-0ubuntu32.3_all.deb ...
Unpacking python3-aptdaemon.gtk3widgets (1.1.1+bzr982-0ubuntu32.3) ...
Selecting previously unselected package im-config.
Preparing to unpack .../287-im-config_0.44-1ubuntu1.3_all.deb ...
Unpacking im-config (0.44-1ubuntu1.3) ...
Selecting previously unselected package language-selector-gnome.
Preparing to unpack .../288-language-selector-gnome_0.204.2_all.deb ...
Unpacking language-selector-gnome (0.204.2) ...
Selecting previously unselected package python3-cups.
Preparing to unpack .../289-python3-cups_1.9.73-3build1_amd64.deb ...
Unpacking python3-cups (1.9.73-3build1) ...
Selecting previously unselected package python3-cupshelpers.
Preparing to unpack .../290-python3-cupshelpers_1.5.12-0ubuntu1.1_all.deb ...
Unpacking python3-cupshelpers (1.5.12-0ubuntu1.1) ...
Selecting previously unselected package python3-cairo:amd64.
Preparing to unpack .../291-python3-cairo_1.16.2-2ubuntu2_amd64.deb ...
Unpacking python3-cairo:amd64 (1.16.2-2ubuntu2) ...
Selecting previously unselected package system-config-printer-common.
Preparing to unpack .../292-system-config-printer-common_1.5.12-0ubuntu1.1_all.deb ...
Unpacking system-config-printer-common (1.5.12-0ubuntu1.1) ...
Selecting previously unselected package system-config-printer.
Preparing to unpack .../293-system-config-printer_1.5.12-0ubuntu1.1_all.deb ...
Unpacking system-config-printer (1.5.12-0ubuntu1.1) ...
Selecting previously unselected package libwhoopsie0:amd64.
Preparing to unpack .../294-libwhoopsie0_0.2.69ubuntu0.3_amd64.deb ...
Unpacking libwhoopsie0:amd64 (0.2.69ubuntu0.3) ...
Selecting previously unselected package whoopsie-preferences.
Preparing to unpack .../295-whoopsie-preferences_22_amd64.deb ...
Unpacking whoopsie-preferences (22) ...
Selecting previously unselected package gnome-control-center.
Preparing to unpack .../296-gnome-control-center_1%3a3.36.5-0ubuntu4.1_amd64.deb ...
Unpacking gnome-control-center (1:3.36.5-0ubuntu4.1) ...
Selecting previously unselected package gnome-control-center-faces.
Preparing to unpack .../297-gnome-control-center-faces_1%3a3.36.5-0ubuntu4.1_all.deb ...
Unpacking gnome-control-center-faces (1:3.36.5-0ubuntu4.1) ...
Selecting previously unselected package gnome-keyring-pkcs11:amd64.
Preparing to unpack .../298-gnome-keyring-pkcs11_3.36.0-1ubuntu1_amd64.deb ...
Unpacking gnome-keyring-pkcs11:amd64 (3.36.0-1ubuntu1) ...
Selecting previously unselected package gnome-menus.
Preparing to unpack .../299-gnome-menus_3.36.0-1ubuntu1_amd64.deb ...
Unpacking gnome-menus (3.36.0-1ubuntu1) ...
Selecting previously unselected package python3-protobuf.
Preparing to unpack .../300-python3-protobuf_3.6.1.3-2ubuntu5.2_amd64.deb ...
Unpacking python3-protobuf (3.6.1.3-2ubuntu5.2) ...
Selecting previously unselected package python3-tz.
Preparing to unpack .../301-python3-tz_2019.3-1ubuntu0.20.04.0_all.deb ...
Unpacking python3-tz (2019.3-1ubuntu0.20.04.0) ...
Selecting previously unselected package python3-rfc3339.
Preparing to unpack .../302-python3-rfc3339_1.1-2_all.deb ...
Unpacking python3-rfc3339 (1.1-2) ...
Selecting previously unselected package python3-macaroonbakery.
Preparing to unpack .../303-python3-macaroonbakery_1.3.1-1_all.deb ...
Unpacking python3-macaroonbakery (1.3.1-1) ...
Selecting previously unselected package gnome-online-accounts.
Preparing to unpack .../304-gnome-online-accounts_3.36.1-0ubuntu1_amd64.deb ...
Unpacking gnome-online-accounts (3.36.1-0ubuntu1) ...
Selecting previously unselected package gnome-startup-applications.
Preparing to unpack .../305-gnome-startup-applications_3.36.0-2ubuntu1_amd64.deb ...
Unpacking gnome-startup-applications (3.36.0-2ubuntu1) ...
Selecting previously unselected package libyelp0:amd64.
Preparing to unpack .../306-libyelp0_3.36.2-0ubuntu1.1_amd64.deb ...
Unpacking libyelp0:amd64 (3.36.2-0ubuntu1.1) ...
Selecting previously unselected package yelp-xsl.
Preparing to unpack .../307-yelp-xsl_3.36.0-1ubuntu0.1_all.deb ...
Unpacking yelp-xsl (3.36.0-1ubuntu0.1) ...
Selecting previously unselected package yelp.
Preparing to unpack .../308-yelp_3.36.2-0ubuntu1.1_amd64.deb ...
Unpacking yelp (3.36.2-0ubuntu1.1) ...
Selecting previously unselected package ubuntu-docs.
Preparing to unpack .../309-ubuntu-docs_20.04.3_all.deb ...
Unpacking ubuntu-docs (20.04.3) ...
Selecting previously unselected package gnome-user-docs.
Preparing to unpack .../310-gnome-user-docs_3.36.2+git20200704-0ubuntu0.1_all.deb ...
Unpacking gnome-user-docs (3.36.2+git20200704-0ubuntu0.1) ...
Selecting previously unselected package aglfn.
Preparing to unpack .../311-aglfn_1.7+git20191031.4036a9c-2_all.deb ...
Unpacking aglfn (1.7+git20191031.4036a9c-2) ...
Selecting previously unselected package gnuplot-data.
Preparing to unpack .../312-gnuplot-data_5.2.8+dfsg1-2_all.deb ...
Unpacking gnuplot-data (5.2.8+dfsg1-2) ...
Selecting previously unselected package liblua5.3-0:amd64.
Preparing to unpack .../313-liblua5.3-0_5.3.3-1.1ubuntu2_amd64.deb ...
Unpacking liblua5.3-0:amd64 (5.3.3-1.1ubuntu2) ...
Selecting previously unselected package libqt5printsupport5:amd64.
Preparing to unpack .../314-libqt5printsupport5_5.12.8+dfsg-0ubuntu2.1_amd64.deb ...
Unpacking libqt5printsupport5:amd64 (5.12.8+dfsg-0ubuntu2.1) ...
Selecting previously unselected package libwxbase3.0-0v5:amd64.
Preparing to unpack .../315-libwxbase3.0-0v5_3.0.4+dfsg-15build1_amd64.deb ...
Unpacking libwxbase3.0-0v5:amd64 (3.0.4+dfsg-15build1) ...
Selecting previously unselected package libwxgtk3.0-gtk3-0v5:amd64.
Preparing to unpack .../316-libwxgtk3.0-gtk3-0v5_3.0.4+dfsg-15build1_amd64.deb ...
Unpacking libwxgtk3.0-gtk3-0v5:amd64 (3.0.4+dfsg-15build1) ...
Selecting previously unselected package gnuplot-qt.
Preparing to unpack .../317-gnuplot-qt_5.2.8+dfsg1-2_amd64.deb ...
Unpacking gnuplot-qt (5.2.8+dfsg1-2) ...
Selecting previously unselected package gnuplot.
Preparing to unpack .../318-gnuplot_5.2.8+dfsg1-2_all.deb ...
Unpacking gnuplot (5.2.8+dfsg1-2) ...
Selecting previously unselected package gstreamer1.0-gl:amd64.
Preparing to unpack .../319-gstreamer1.0-gl_1.16.3-0ubuntu1.4_amd64.deb ...
Unpacking gstreamer1.0-gl:amd64 (1.16.3-0ubuntu1.4) ...
Selecting previously unselected package gstreamer1.0-pulseaudio:amd64.
Preparing to unpack .../320-gstreamer1.0-pulseaudio_1.16.3-0ubuntu1.3_amd64.deb ...
Unpacking gstreamer1.0-pulseaudio:amd64 (1.16.3-0ubuntu1.3) ...
Selecting previously unselected package ibus-data.
Preparing to unpack .../321-ibus-data_1.5.22-2ubuntu2.1_all.deb ...
Unpacking ibus-data (1.5.22-2ubuntu2.1) ...
Selecting previously unselected package python3-ibus-1.0.
Preparing to unpack .../322-python3-ibus-1.0_1.5.22-2ubuntu2.1_all.deb ...
Unpacking python3-ibus-1.0 (1.5.22-2ubuntu2.1) ...
Selecting previously unselected package ibus.
Preparing to unpack .../323-ibus_1.5.22-2ubuntu2.1_amd64.deb ...
Unpacking ibus (1.5.22-2ubuntu2.1) ...
Selecting previously unselected package libgtk2.0-common.
Preparing to unpack .../324-libgtk2.0-common_2.24.32-4ubuntu4.1_all.deb ...
Unpacking libgtk2.0-common (2.24.32-4ubuntu4.1) ...
Selecting previously unselected package libgtk2.0-0:amd64.
Preparing to unpack .../325-libgtk2.0-0_2.24.32-4ubuntu4.1_amd64.deb ...
Unpacking libgtk2.0-0:amd64 (2.24.32-4ubuntu4.1) ...
Selecting previously unselected package ibus-gtk:amd64.
Preparing to unpack .../326-ibus-gtk_1.5.22-2ubuntu2.1_amd64.deb ...
Unpacking ibus-gtk:amd64 (1.5.22-2ubuntu2.1) ...
Selecting previously unselected package ibus-gtk3:amd64.
Preparing to unpack .../327-ibus-gtk3_1.5.22-2ubuntu2.1_amd64.deb ...
Unpacking ibus-gtk3:amd64 (1.5.22-2ubuntu2.1) ...
Selecting previously unselected package iio-sensor-proxy.
Preparing to unpack .../328-iio-sensor-proxy_2.8-1ubuntu2_amd64.deb ...
Unpacking iio-sensor-proxy (2.8-1ubuntu2) ...
Selecting previously unselected package libdbusmenu-glib4:amd64.
Preparing to unpack .../329-libdbusmenu-glib4_16.04.1+18.10.20180917-0ubuntu6_amd64.deb ...
Unpacking libdbusmenu-glib4:amd64 (16.04.1+18.10.20180917-0ubuntu6) ...
Selecting previously unselected package libdbusmenu-gtk3-4:amd64.
Preparing to unpack .../330-libdbusmenu-gtk3-4_16.04.1+18.10.20180917-0ubuntu6_amd64.deb ...
Unpacking libdbusmenu-gtk3-4:amd64 (16.04.1+18.10.20180917-0ubuntu6) ...
Selecting previously unselected package libappindicator3-1.
Preparing to unpack .../331-libappindicator3-1_12.10.1+20.04.20200408.1-0ubuntu1_amd64.deb ...
Unpacking libappindicator3-1 (12.10.1+20.04.20200408.1-0ubuntu1) ...
Selecting previously unselected package libstemmer0d:amd64.
Preparing to unpack .../332-libstemmer0d_0+svn585-2_amd64.deb ...
Unpacking libstemmer0d:amd64 (0+svn585-2) ...
Selecting previously unselected package libappstream4:amd64.
Preparing to unpack .../333-libappstream4_0.12.10-2_amd64.deb ...
Unpacking libappstream4:amd64 (0.12.10-2) ...
Selecting previously unselected package libbluetooth3:amd64.
Preparing to unpack .../334-libbluetooth3_5.53-0ubuntu3.9_amd64.deb ...
Unpacking libbluetooth3:amd64 (5.53-0ubuntu3.9) ...
Selecting previously unselected package libcanberra-pulse:amd64.
Preparing to unpack .../335-libcanberra-pulse_0.30-7ubuntu1_amd64.deb ...
Unpacking libcanberra-pulse:amd64 (0.30-7ubuntu1) ...
Selecting previously unselected package libclutter-1.0-common.
Preparing to unpack .../336-libclutter-1.0-common_1.26.4+dfsg-1_all.deb ...
Unpacking libclutter-1.0-common (1.26.4+dfsg-1) ...
Selecting previously unselected package libcogl-common.
Preparing to unpack .../337-libcogl-common_1.22.6-1_all.deb ...
Unpacking libcogl-common (1.22.6-1) ...
Selecting previously unselected package libfprint-2-2:amd64.
Preparing to unpack .../338-libfprint-2-2_1%3a1.90.2+tod1-0ubuntu1~20.04.10_amd64.deb ...
Unpacking libfprint-2-2:amd64 (1:1.90.2+tod1-0ubuntu1~20.04.10) ...
Selecting previously unselected package libgail18:amd64.
Preparing to unpack .../339-libgail18_2.24.32-4ubuntu4.1_amd64.deb ...
Unpacking libgail18:amd64 (2.24.32-4ubuntu4.1) ...
Selecting previously unselected package libgail-common:amd64.
Preparing to unpack .../340-libgail-common_2.24.32-4ubuntu4.1_amd64.deb ...
Unpacking libgail-common:amd64 (2.24.32-4ubuntu4.1) ...
Selecting previously unselected package libgee-0.8-2:amd64.
Preparing to unpack .../341-libgee-0.8-2_0.20.3-1_amd64.deb ...
Unpacking libgee-0.8-2:amd64 (0.20.3-1) ...
Selecting previously unselected package libgphoto2-l10n.
Preparing to unpack .../342-libgphoto2-l10n_2.5.25-0ubuntu0.1_all.deb ...
Unpacking libgphoto2-l10n (2.5.25-0ubuntu0.1) ...
Selecting previously unselected package libgssdp-1.2-0:amd64.
Preparing to unpack .../343-libgssdp-1.2-0_1.2.3-0ubuntu0.20.04.1_amd64.deb ...
Unpacking libgssdp-1.2-0:amd64 (1.2.3-0ubuntu0.20.04.1) ...
Selecting previously unselected package libgtk2.0-bin.
Preparing to unpack .../344-libgtk2.0-bin_2.24.32-4ubuntu4.1_amd64.deb ...
Unpacking libgtk2.0-bin (2.24.32-4ubuntu4.1) ...
Selecting previously unselected package libgupnp-1.2-0:amd64.
Preparing to unpack .../345-libgupnp-1.2-0_1.2.4-0ubuntu1_amd64.deb ...
Unpacking libgupnp-1.2-0:amd64 (1.2.4-0ubuntu1) ...
Selecting previously unselected package libgupnp-av-1.0-2.
Preparing to unpack .../346-libgupnp-av-1.0-2_0.12.11-2_amd64.deb ...
Unpacking libgupnp-av-1.0-2 (0.12.11-2) ...
Selecting previously unselected package libgupnp-dlna-2.0-3.
Preparing to unpack .../347-libgupnp-dlna-2.0-3_0.10.5-4_amd64.deb ...
Unpacking libgupnp-dlna-2.0-3 (0.10.5-4) ...
Selecting previously unselected package libmbim-glib4:amd64.
Preparing to unpack .../348-libmbim-glib4_1.26.2-1~ubuntu20.04.1_amd64.deb ...
Unpacking libmbim-glib4:amd64 (1.26.2-1~ubuntu20.04.1) ...
Selecting previously unselected package libmbim-proxy.
Preparing to unpack .../349-libmbim-proxy_1.26.2-1~ubuntu20.04.1_amd64.deb ...
Unpacking libmbim-proxy (1.26.2-1~ubuntu20.04.1) ...
Selecting previously unselected package libmediaart-2.0-0:amd64.
Preparing to unpack .../350-libmediaart-2.0-0_1.9.4-2_amd64.deb ...
Unpacking libmediaart-2.0-0:amd64 (1.9.4-2) ...
Selecting previously unselected package libndp0:amd64.
Preparing to unpack .../351-libndp0_1.7-0ubuntu1.1_amd64.deb ...
Unpacking libndp0:amd64 (1.7-0ubuntu1.1) ...
Selecting previously unselected package libnss-mdns:amd64.
Preparing to unpack .../352-libnss-mdns_0.14.1-1ubuntu1_amd64.deb ...
Unpacking libnss-mdns:amd64 (0.14.1-1ubuntu1) ...
Selecting previously unselected package libpam-gnome-keyring:amd64.
Preparing to unpack .../353-libpam-gnome-keyring_3.36.0-1ubuntu1_amd64.deb ...
Unpacking libpam-gnome-keyring:amd64 (3.36.0-1ubuntu1) ...
Selecting previously unselected package libqmi-glib5:amd64.
Preparing to unpack .../354-libqmi-glib5_1.30.4-1~ubuntu20.04.1_amd64.deb ...
Unpacking libqmi-glib5:amd64 (1.30.4-1~ubuntu20.04.1) ...
Selecting previously unselected package libqmi-proxy.
Preparing to unpack .../355-libqmi-proxy_1.30.4-1~ubuntu20.04.1_amd64.deb ...
Unpacking libqmi-proxy (1.30.4-1~ubuntu20.04.1) ...
Selecting previously unselected package libteamdctl0:amd64.
Preparing to unpack .../356-libteamdctl0_1.30-1_amd64.deb ...
Unpacking libteamdctl0:amd64 (1.30-1) ...
Selecting previously unselected package libwacom-bin.
Preparing to unpack .../357-libwacom-bin_1.3-2ubuntu3_amd64.deb ...
Unpacking libwacom-bin (1.3-2ubuntu3) ...
Selecting previously unselected package libxatracker2:amd64.
Preparing to unpack .../358-libxatracker2_21.2.6-0ubuntu0.1~20.04.2_amd64.deb ...
Unpacking libxatracker2:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...
Selecting previously unselected package libxcb-xv0:amd64.
Preparing to unpack .../359-libxcb-xv0_1.14-2_amd64.deb ...
Unpacking libxcb-xv0:amd64 (1.14-2) ...
Selecting previously unselected package libxvmc1:amd64.
Preparing to unpack .../360-libxvmc1_2%3a1.0.12-2_amd64.deb ...
Unpacking libxvmc1:amd64 (2:1.0.12-2) ...
Selecting previously unselected package modemmanager.
Preparing to unpack .../361-modemmanager_1.18.6-1~ubuntu20.04.1_amd64.deb ...
Unpacking modemmanager (1.18.6-1~ubuntu20.04.1) ...
Selecting previously unselected package mousetweaks.
Preparing to unpack .../362-mousetweaks_3.32.0-2_amd64.deb ...
Unpacking mousetweaks (3.32.0-2) ...
Selecting previously unselected package wpasupplicant.
Preparing to unpack .../363-wpasupplicant_2%3a2.9-1ubuntu4.6_amd64.deb ...
Unpacking wpasupplicant (2:2.9-1ubuntu4.6) ...
Selecting previously unselected package network-manager.
Preparing to unpack .../364-network-manager_1.22.10-1ubuntu2.4_amd64.deb ...
Unpacking network-manager (1.22.10-1ubuntu2.4) ...
Selecting previously unselected package network-manager-gnome.
Preparing to unpack .../365-network-manager-gnome_1.8.24-1ubuntu3_amd64.deb ...
Unpacking network-manager-gnome (1.8.24-1ubuntu3) ...
Selecting previously unselected package ppp.
Preparing to unpack .../366-ppp_2.4.7-2+4.1ubuntu5.1_amd64.deb ...
Unpacking ppp (2.4.7-2+4.1ubuntu5.1) ...
Selecting previously unselected package pptp-linux.
Preparing to unpack .../367-pptp-linux_1.10.0-1build1_amd64.deb ...
Unpacking pptp-linux (1.10.0-1build1) ...
Selecting previously unselected package network-manager-pptp.
Preparing to unpack .../368-network-manager-pptp_1.2.8-2_amd64.deb ...
Unpacking network-manager-pptp (1.2.8-2) ...
Selecting previously unselected package packagekit.
Preparing to unpack .../369-packagekit_1.1.13-2ubuntu1.1_amd64.deb ...
Unpacking packagekit (1.1.13-2ubuntu1.1) ...
Selecting previously unselected package packagekit-tools.
Preparing to unpack .../370-packagekit-tools_1.1.13-2ubuntu1.1_amd64.deb ...
Unpacking packagekit-tools (1.1.13-2ubuntu1.1) ...
Selecting previously unselected package python3-systemd.
Preparing to unpack .../371-python3-systemd_234-3build2_amd64.deb ...
Unpacking python3-systemd (234-3build2) ...
Selecting previously unselected package qt5-gtk-platformtheme:amd64.
Preparing to unpack .../372-qt5-gtk-platformtheme_5.12.8+dfsg-0ubuntu2.1_amd64.deb ...
Unpacking qt5-gtk-platformtheme:amd64 (5.12.8+dfsg-0ubuntu2.1) ...
Selecting previously unselected package qttranslations5-l10n.
Preparing to unpack .../373-qttranslations5-l10n_5.12.8-0ubuntu1_all.deb ...
Unpacking qttranslations5-l10n (5.12.8-0ubuntu1) ...
Selecting previously unselected package rtkit.
Preparing to unpack .../374-rtkit_0.12-4_amd64.deb ...
Unpacking rtkit (0.12-4) ...
Selecting previously unselected package sane-utils.
Preparing to unpack .../375-sane-utils_1.0.29-0ubuntu5.2_amd64.deb ...
Unpacking sane-utils (1.0.29-0ubuntu5.2) ...
Selecting previously unselected package switcheroo-control.
Preparing to unpack .../376-switcheroo-control_2.1-1_amd64.deb ...
Unpacking switcheroo-control (2.1-1) ...
Selecting previously unselected package system-config-printer-udev.
Preparing to unpack .../377-system-config-printer-udev_1.5.12-0ubuntu1.1_amd64.deb ...
Unpacking system-config-printer-udev (1.5.12-0ubuntu1.1) ...
Selecting previously unselected package usbmuxd.
Preparing to unpack .../378-usbmuxd_1.1.1~git20191130.9af2b12-1_amd64.deb ...
Unpacking usbmuxd (1.1.1~git20191130.9af2b12-1) ...
Selecting previously unselected package wamerican.
Preparing to unpack .../379-wamerican_2018.04.16-1_all.deb ...
Unpacking wamerican (2018.04.16-1) ...
Selecting previously unselected package xfonts-encodings.
Preparing to unpack .../380-xfonts-encodings_1%3a1.0.5-0ubuntu1_all.deb ...
Unpacking xfonts-encodings (1:1.0.5-0ubuntu1) ...
Selecting previously unselected package xfonts-utils.
Preparing to unpack .../381-xfonts-utils_1%3a7.7+6_amd64.deb ...
Unpacking xfonts-utils (1:7.7+6) ...
Selecting previously unselected package xfonts-base.
Preparing to unpack .../382-xfonts-base_1%3a1.0.5_all.deb ...
Unpacking xfonts-base (1:1.0.5) ...
Selecting previously unselected package xserver-xephyr.
Preparing to unpack .../383-xserver-xephyr_2%3a1.20.13-1ubuntu1~20.04.20_amd64.deb ...
Unpacking xserver-xephyr (2:1.20.13-1ubuntu1~20.04.20) ...
Selecting previously unselected package xserver-xorg-core.
Preparing to unpack .../384-xserver-xorg-core_2%3a1.20.13-1ubuntu1~20.04.20_amd64.deb ...
Unpacking xserver-xorg-core (2:1.20.13-1ubuntu1~20.04.20) ...
Selecting previously unselected package xserver-xorg-input-libinput.
Preparing to unpack .../385-xserver-xorg-input-libinput_0.29.0-1_amd64.deb ...
Unpacking xserver-xorg-input-libinput (0.29.0-1) ...
Selecting previously unselected package xserver-xorg-input-all.
Preparing to unpack .../386-xserver-xorg-input-all_1%3a7.7+19ubuntu14_amd64.deb ...
Unpacking xserver-xorg-input-all (1:7.7+19ubuntu14) ...
Selecting previously unselected package xserver-xorg-input-wacom.
Preparing to unpack .../387-xserver-xorg-input-wacom_1%3a0.39.0-0ubuntu1_amd64.deb ...
Unpacking xserver-xorg-input-wacom (1:0.39.0-0ubuntu1) ...
Selecting previously unselected package xserver-xorg.
Preparing to unpack .../388-xserver-xorg_1%3a7.7+19ubuntu14_amd64.deb ...
Unpacking xserver-xorg (1:7.7+19ubuntu14) ...
Selecting previously unselected package xserver-xorg-video-amdgpu.
Preparing to unpack .../389-xserver-xorg-video-amdgpu_19.1.0-1ubuntu0.1_amd64.deb ...
Unpacking xserver-xorg-video-amdgpu (19.1.0-1ubuntu0.1) ...
Selecting previously unselected package xserver-xorg-video-radeon.
Preparing to unpack .../390-xserver-xorg-video-radeon_1%3a19.1.0-1_amd64.deb ...
Unpacking xserver-xorg-video-radeon (1:19.1.0-1) ...
Selecting previously unselected package xserver-xorg-video-ati.
Preparing to unpack .../391-xserver-xorg-video-ati_1%3a19.1.0-1_amd64.deb ...
Unpacking xserver-xorg-video-ati (1:19.1.0-1) ...
Selecting previously unselected package xserver-xorg-video-fbdev.
Preparing to unpack .../392-xserver-xorg-video-fbdev_1%3a0.5.0-1ubuntu1_amd64.deb ...
Unpacking xserver-xorg-video-fbdev (1:0.5.0-1ubuntu1) ...
Selecting previously unselected package xserver-xorg-video-nouveau.
Preparing to unpack .../393-xserver-xorg-video-nouveau_1%3a1.0.16-1_amd64.deb ...
Unpacking xserver-xorg-video-nouveau (1:1.0.16-1) ...
Selecting previously unselected package xserver-xorg-video-vesa.
Preparing to unpack .../394-xserver-xorg-video-vesa_1%3a2.4.0-2_amd64.deb ...
Unpacking xserver-xorg-video-vesa (1:2.4.0-2) ...
Selecting previously unselected package xserver-xorg-video-vmware.
Preparing to unpack .../395-xserver-xorg-video-vmware_1%3a13.3.0-3_amd64.deb ...
Unpacking xserver-xorg-video-vmware (1:13.3.0-3) ...
Selecting previously unselected package xserver-xorg-video-all.
Preparing to unpack .../396-xserver-xorg-video-all_1%3a7.7+19ubuntu14_amd64.deb ...
Unpacking xserver-xorg-video-all (1:7.7+19ubuntu14) ...
Selecting previously unselected package xserver-xorg-video-intel.
Preparing to unpack .../397-xserver-xorg-video-intel_2%3a2.99.917+git20200226-1_amd64.deb ...
Unpacking xserver-xorg-video-intel (2:2.99.917+git20200226-1) ...
Selecting previously unselected package xserver-xorg-video-qxl.
Preparing to unpack .../398-xserver-xorg-video-qxl_0.1.5+git20200331-1_amd64.deb ...
Unpacking xserver-xorg-video-qxl (0.1.5+git20200331-1) ...
Selecting previously unselected package cups-pk-helper.
Preparing to unpack .../399-cups-pk-helper_0.2.6-1ubuntu3_amd64.deb ...
Unpacking cups-pk-helper (0.2.6-1ubuntu3) ...
Selecting previously unselected package fprintd.
Preparing to unpack .../400-fprintd_1.90.9-1~ubuntu20.04.1_amd64.deb ...
Unpacking fprintd (1.90.9-1~ubuntu20.04.1) ...
Selecting previously unselected package ippusbxd.
Preparing to unpack .../401-ippusbxd_1.34-2ubuntu1_amd64.deb ...
Unpacking ippusbxd (1.34-2ubuntu1) ...
Selecting previously unselected package libcanberra-gtk3-module:amd64.
Preparing to unpack .../402-libcanberra-gtk3-module_0.30-7ubuntu1_amd64.deb ...
Unpacking libcanberra-gtk3-module:amd64 (0.30-7ubuntu1) ...
Selecting previously unselected package libpam-fprintd:amd64.
Preparing to unpack .../403-libpam-fprintd_1.90.9-1~ubuntu20.04.1_amd64.deb ...
Unpacking libpam-fprintd:amd64 (1.90.9-1~ubuntu20.04.1) ...
Selecting previously unselected package librygel-core-2.6-2:amd64.
Preparing to unpack .../404-librygel-core-2.6-2_0.38.3-1ubuntu1_amd64.deb ...
Unpacking librygel-core-2.6-2:amd64 (0.38.3-1ubuntu1) ...
Selecting previously unselected package librygel-db-2.6-2:amd64.
Preparing to unpack .../405-librygel-db-2.6-2_0.38.3-1ubuntu1_amd64.deb ...
Unpacking librygel-db-2.6-2:amd64 (0.38.3-1ubuntu1) ...
Selecting previously unselected package librygel-renderer-2.6-2:amd64.
Preparing to unpack .../406-librygel-renderer-2.6-2_0.38.3-1ubuntu1_amd64.deb ...
Unpacking librygel-renderer-2.6-2:amd64 (0.38.3-1ubuntu1) ...
Selecting previously unselected package librygel-server-2.6-2:amd64.
Preparing to unpack .../407-librygel-server-2.6-2_0.38.3-1ubuntu1_amd64.deb ...
Unpacking librygel-server-2.6-2:amd64 (0.38.3-1ubuntu1) ...
Selecting previously unselected package libsbc1:amd64.
Preparing to unpack .../408-libsbc1_1.4-1_amd64.deb ...
Unpacking libsbc1:amd64 (1.4-1) ...
Selecting previously unselected package mobile-broadband-provider-info.
Preparing to unpack .../409-mobile-broadband-provider-info_20190618-3_all.deb ...
Unpacking mobile-broadband-provider-info (20190618-3) ...
Selecting previously unselected package pulseaudio-module-bluetooth.
Preparing to unpack .../410-pulseaudio-module-bluetooth_1%3a13.99.1-1ubuntu3.13_amd64.deb ...
Unpacking pulseaudio-module-bluetooth (1:13.99.1-1ubuntu3.13) ...
Selecting previously unselected package rygel.
Preparing to unpack .../411-rygel_0.38.3-1ubuntu1_amd64.deb ...
Unpacking rygel (0.38.3-1ubuntu1) ...
Selecting previously unselected package usb-modeswitch-data.
Preparing to unpack .../412-usb-modeswitch-data_20191128-3_all.deb ...
Unpacking usb-modeswitch-data (20191128-3) ...
Selecting previously unselected package usb-modeswitch.
Preparing to unpack .../413-usb-modeswitch_2.5.2+repack0-2ubuntu3_amd64.deb ...
Unpacking usb-modeswitch (2.5.2+repack0-2ubuntu3) ...
Selecting previously unselected package xserver-xorg-legacy.
Preparing to unpack .../414-xserver-xorg-legacy_2%3a1.20.13-1ubuntu1~20.04.20_amd64.deb ...
Unpacking xserver-xorg-legacy (2:1.20.13-1ubuntu1~20.04.20) ...
Setting up python3-entrypoints (0.3-2ubuntu1) ...
Setting up libpwquality-common (1.4.2-1build1) ...
Setting up libgoa-1.0-common (3.36.1-0ubuntu1) ...
Setting up bubblewrap (0.4.0-1ubuntu4.1) ...
Setting up mousetweaks (3.32.0-2) ...
Setting up liblmdb0:amd64 (0.9.24-1) ...
Setting up libtext-iconv-perl (1.7-7) ...
Setting up libgweather-common (3.36.1-1~ubuntu20.04.1) ...
Setting up libwhoopsie-preferences0 (22) ...
Setting up libxcb-res0:amd64 (1.14-2) ...
Setting up mysql-common (5.8+1.0.5ubuntu2) ...
update-alternatives: using /etc/mysql/my.cnf.fallback to provide /etc/mysql/my.cnf (my.cnf) in auto mode
Setting up session-migration (0.3.5) ...
Setting up iio-sensor-proxy (2.8-1ubuntu2) ...
Setting up libmysqlclient21:amd64 (8.0.42-0ubuntu0.20.04.1) ...
Setting up libdouble-conversion3:amd64 (3.1.5-4ubuntu1) ...
Setting up libsbc1:amd64 (1.4-1) ...
Setting up libasound2-plugins:amd64 (1.2.2-1ubuntu1) ...
Setting up pci.ids (0.0~2020.03.20-1) ...
Setting up libnewt0.52:amd64 (0.52.21-4ubuntu2) ...
update-alternatives: using /etc/newt/palette.ubuntu to provide /etc/newt/palette (newt-palette) in auto mode
Setting up python3-cairo:amd64 (1.16.2-2ubuntu2) ...
Setting up desktop-file-utils (0.24-1ubuntu3) ...
Setting up dconf-cli (0.36.0-1) ...
Setting up libxpm4:amd64 (1:3.5.12-1ubuntu0.20.04.2) ...
Setting up libplist3:amd64 (2.1.0-4build2) ...
Setting up gnome-settings-daemon-common (3.36.1-0ubuntu1.1) ...
Setting up libxcb-xinput0:amd64 (1.14-2) ...
Setting up libwoff1:amd64 (1.0.2-1build2) ...
Setting up libhyphen0:amd64 (2.8.8-7) ...
Setting up libgdm1 (3.36.3-0ubuntu0.20.04.4) ...
Setting up mobile-broadband-provider-info (20190618-3) ...
Setting up python3-jwt (1.7.1-2ubuntu2.1) ...
Setting up libmozjs-68-0:amd64 (68.6.0-1ubuntu1) ...
Setting up ubuntu-wallpapers-focal (20.04.2-0ubuntu1) ...
Setting up yaru-theme-gnome-shell (**********) ...
update-alternatives: using /usr/share/gnome-shell/theme/Yaru/gnome-shell-theme.gresource to provide /usr/share/gnome-shell/gdm3-theme.gresource (gdm3-theme.gresource) in auto mode
Setting up gnome-menus (3.36.0-1ubuntu1) ...
Setting up python3-problem-report (2.20.11-0ubuntu27.31) ...
Setting up libip6tc2:amd64 (1.8.4-3ubuntu2.1) ...
Setting up libaspell15:amd64 (0.60.8-1ubuntu0.1) ...
Setting up distro-info-data (0.43ubuntu1.18) ...
Setting up libsnmp-base (5.8+dfsg-2ubuntu2.9) ...
Setting up libgusb2:amd64 (0.3.4-0.1) ...
Setting up libmaxminddb0:amd64 (1.4.2-0ubuntu1.20.04.1) ...
Setting up libdbusmenu-glib4:amd64 (16.04.1+18.10.20180917-0ubuntu6) ...
Setting up wireless-regdb (2024.07.04-0ubuntu1~20.04.1) ...
Setting up libibus-1.0-5:amd64 (1.5.22-2ubuntu2.1) ...
Setting up libgdata-common (0.17.12-1) ...
Setting up libxcb-keysyms1:amd64 (0.4.0-1build1) ...
Setting up libgnome-autoar-0-0:amd64 (0.2.3-2ubuntu0.4) ...
Setting up libjavascriptcoregtk-4.0-18:amd64 (2.38.6-0ubuntu0.20.04.1) ...
Setting up libwebrtc-audio-processing1:amd64 (0.3.1-0ubuntu3) ...
Setting up libpackagekit-glib2-18:amd64 (1.1.13-2ubuntu1.1) ...
Setting up libcolord-gtk1:amd64 (0.2.0-0ubuntu1) ...
Setting up gnome-shell-common (3.36.9-0ubuntu0.20.04.5) ...
Setting up libpulsedsp:amd64 (1:13.99.1-1ubuntu3.13) ...
Setting up libcolorhug2:amd64 (1.4.4-2) ...
Setting up libxcb-render-util0:amd64 (0.3.9-1build1) ...
Setting up apg (2.2.3.dfsg.1-5) ...
Setting up python3-lazr.uri (1.0.3-4build1) ...
Setting up libcanberra-gtk3-0:amd64 (0.30-7ubuntu1) ...
Setting up libxcb-icccm4:amd64 (0.4.1-1.1) ...
Setting up libwhoopsie0:amd64 (0.2.69ubuntu0.3) ...
Setting up kmod (27-1ubuntu2.1) ...
Setting up ibus-data (1.5.22-2ubuntu2.1) ...
Setting up gir1.2-gdesktopenums-3.0:amd64 (3.36.0-1ubuntu1) ...
Setting up libxvmc1:amd64 (2:1.0.12-2) ...
Setting up libunwind8:amd64 (1.2.1-9ubuntu0.1) ...
Setting up libgphoto2-l10n (2.5.25-0ubuntu0.1) ...
Setting up gir1.2-soup-2.4:amd64 (2.70.0-1ubuntu0.5) ...
Setting up libcogl20:amd64 (1.22.6-1) ...
Setting up libcogl-pango20:amd64 (1.22.6-1) ...
Setting up apport-symptoms (0.23) ...
Setting up libcanberra-gtk3-module:amd64 (0.30-7ubuntu1) ...
Setting up python3-tz (2019.3-1ubuntu0.20.04.0) ...
Setting up colord-data (1.4.4-2) ...
Setting up libxcb-util1:amd64 (0.4.0-0ubuntu3) ...
Setting up libjansson4:amd64 (2.12-1build1) ...
Setting up libxatracker2:amd64 (21.2.6-0ubuntu0.1~20.04.2) ...
Setting up libxcb-xkb1:amd64 (1.14-2) ...
Setting up libxcb-image0:amd64 (0.4.0-1build1) ...
Setting up dns-root-data (2024071801~ubuntu0.20.04.1) ...
Setting up libtalloc2:amd64 (2.3.3-0ubuntu0.20.04.1) ...
Setting up gnome-desktop3-data (3.36.8-0ubuntu1) ...
Setting up python3-simplejson (3.16.0-2ubuntu2) ...
Setting up ippusbxd (1.34-2ubuntu1) ...
Setting up libgtop2-common (2.40.0-2) ...
Setting up python3-cups (1.9.73-3build1) ...
Setting up gir1.2-packagekitglib-1.0 (1.1.13-2ubuntu1.1) ...
Setting up p11-kit-modules:amd64 (0.23.20-1ubuntu0.1) ...
Setting up libfontenc1:amd64 (1:1.1.4-0ubuntu1) ...
Setting up mutter-common (3.36.9-0ubuntu0.20.04.2) ...
Setting up libclutter-1.0-common (1.26.4+dfsg-1) ...
Setting up libmediaart-2.0-0:amd64 (1.9.4-2) ...
Setting up libgstreamer-gl1.0-0:amd64 (1.16.3-0ubuntu1.4) ...
Setting up libxcb-xinerama0:amd64 (1.14-2) ...
Setting up libgnomekbd-common (3.26.1-1) ...
Setting up python3-chardet (3.0.4-4build1) ...
Setting up qttranslations5-l10n (5.12.8-0ubuntu1) ...
Setting up emacsen-common (3.0.4) ...
Setting up libtevent0:amd64 (0.11.0-0ubuntu0.20.04.1) ...
Setting up libpcap0.8:amd64 (1.9.1-3ubuntu1.20.04.1) ...
Setting up python3-certifi (2019.11.28-1) ...
Setting up gnome-control-center-data (1:3.36.5-0ubuntu4.1) ...
Setting up libgsound0:amd64 (1.0.2-4) ...
Setting up libnotify4:amd64 (0.7.9-1ubuntu3.20.04.2) ...
Setting up aglfn (1.7+git20191031.4036a9c-2) ...
Setting up libteamdctl0:amd64 (1.30-1) ...
Setting up libpulse-mainloop-glib0:amd64 (1:13.99.1-1ubuntu3.13) ...
Setting up python3-wadllib (1.3.3-3build1) ...
Setting up wamerican (2018.04.16-1) ...
debconf: unable to initialize frontend: Dialog
debconf: (No usable dialog-like program is installed, so the dialog based frontend cannot be used. at /usr/share/perl5/Debconf/FrontEnd/Dialog.pm line 76.)
debconf: falling back to frontend: Readline
Setting up pulseaudio-utils (1:13.99.1-1ubuntu3.13) ...
Setting up xfonts-encodings (1:1.0.5-0ubuntu1) ...
Setting up zenity-common (3.32.0-5) ...
Setting up libexif12:amd64 (0.6.21-6ubuntu0.4) ...
Setting up libgd3:amd64 (2.2.5-5.2ubuntu2.4) ...
Setting up libxkbcommon-x11-0:amd64 (0.10.0-1) ...
Setting up libcogl-common (1.22.6-1) ...
Setting up usb-modeswitch-data (20191128-3) ...
Setting up libmnl0:amd64 (1.0.4-2) ...
Setting up libgee-0.8-2:amd64 (0.20.3-1) ...
Setting up udev (245.4-4ubuntu3.24) ...
invoke-rc.d: could not determine current runlevel
invoke-rc.d: policy-rc.d denied execution of start.
Setting up python3-idna (2.8-1ubuntu0.1) ...
Setting up usb.ids (2020.03.19-1) ...
Setting up libxt6:amd64 (1:1.1.5-1) ...
Setting up libxtables12:amd64 (1.8.4-3ubuntu2.1) ...
Setting up libqt5core5a:amd64 (5.12.8+dfsg-0ubuntu2.1) ...
Setting up libnm0:amd64 (1.22.10-1ubuntu2.4) ...
Setting up libmtdev1:amd64 (1.1.5-1.1) ...
Setting up libcrack2:amd64 (2.9.6-3.2) ...
Setting up python3-urllib3 (1.25.8-2ubuntu0.4) ...
Setting up im-config (0.44-1ubuntu1.3) ...
Setting up ppp (2.4.7-2+4.1ubuntu5.1) ...
Created symlink /etc/systemd/system/multi-user.target.wants/pppd-dns.service → /lib/systemd/system/pppd-dns.service.
Setting up libpci3:amd64 (1:3.6.4-1ubuntu0.20.04.1) ...
Setting up xdg-dbus-proxy (0.1.2-1) ...
Setting up python-apt-common (2.0.1ubuntu0.20.04.1) ...
Setting up gir1.2-notify-0.7:amd64 (0.7.9-1ubuntu3.20.04.2) ...
Setting up gnome-keyring-pkcs11:amd64 (3.36.0-1ubuntu1) ...
Setting up gnome-control-center-faces (1:3.36.5-0ubuntu4.1) ...
Setting up libmm-glib0:amd64 (1.18.6-1~ubuntu20.04.1) ...
Setting up libusbmuxd6:amd64 (2.0.1-2) ...
Setting up dbus-x11 (1.12.16-2ubuntu2.3) ...
Setting up libbluetooth3:amd64 (5.53-0ubuntu3.9) ...
Setting up libqt5dbus5:amd64 (5.12.8+dfsg-0ubuntu2.1) ...
Setting up libnfnetlink0:amd64 (1.0.1-3build1) ...
Setting up python3-defer (1.0.6-2.1) ...
Setting up libxcb-xv0:amd64 (1.14-2) ...
Setting up liblua5.3-0:amd64 (5.3.3-1.1ubuntu2) ...
Setting up libnl-genl-3-200:amd64 (3.4.0-1ubuntu0.1) ...
Setting up libspeexdsp1:amd64 (1.2~rc1.2-1.1ubuntu1.20.04.1) ...
Setting up libxslt1.1:amd64 (1.1.34-4ubuntu0.20.04.3) ...
Setting up switcheroo-control (2.1-1) ...
Created symlink /etc/systemd/system/graphical.target.wants/switcheroo-control.service → /lib/systemd/system/switcheroo-control.service.
Setting up gnome-session-common (3.36.0-2ubuntu1) ...
Setting up python3-httplib2 (0.14.0-1ubuntu1) ...
Setting up libgtop-2.0-11:amd64 (2.40.0-2) ...
Setting up libupower-glib3:amd64 (0.99.11-1build2) ...
Setting up ubuntu-wallpapers (20.04.2-0ubuntu1) ...
Setting up libical3:amd64 (3.0.8-1) ...
Setting up cheese-common (3.34.0-1ubuntu1) ...
Setting up gir1.2-json-1.0:amd64 (1.4.4-2ubuntu2) ...
Setting up libwxbase3.0-0v5:amd64 (3.0.4+dfsg-15build1) ...
Setting up sgml-base (1.29.1) ...
Setting up libgtk2.0-common (2.24.32-4ubuntu4.1) ...
Setting up libprotobuf17:amd64 (3.6.1.3-2ubuntu5.2) ...
Setting up usb-modeswitch (2.5.2+repack0-2ubuntu3) ...
Setting up libstemmer0d:amd64 (0+svn585-2) ...
Setting up aptdaemon-data (1.1.1+bzr982-0ubuntu32.3) ...
Setting up gir1.2-vte-2.91:amd64 (0.60.3-0ubuntu1~20.5) ...
Setting up libxkbfile1:amd64 (1:1.1.0-1) ...
Setting up libgphoto2-port12:amd64 (2.5.25-0ubuntu0.1) ...
Setting up libndp0:amd64 (1.7-0ubuntu1.1) ...
Setting up gstreamer1.0-pulseaudio:amd64 (1.16.3-0ubuntu1.3) ...
Setting up keyboard-configuration (1.194ubuntu3) ...
debconf: unable to initialize frontend: Dialog
debconf: (No usable dialog-like program is installed, so the dialog based frontend cannot be used. at /usr/share/perl5/Debconf/FrontEnd/Dialog.pm line 76.)
debconf: falling back to frontend: Readline
Configuring keyboard-configuration
----------------------------------

The layout of keyboards varies per country, with some countries having multiple
common layouts. Please select the country of origin for the keyboard of this
computer.

  1. Afghani
  2. Albanian
  3. Amharic
  4. Arabic
  5. Arabic (Morocco)
  6. Arabic (Syria)
  7. Armenian
  8. Azerbaijani
  9. Bambara
  10. Bangla
  11. Belarusian
  12. Belgian
  13. Berber (Algeria, Latin)
  14. Bosnian
  15. Braille
  16. Bulgarian
  17. Burmese
  18. Chinese
  19. Croatian
  20. Czech
  21. Danish
  22. Dhivehi
  23. Dutch
  24. Dzongkha
  25. English (Australian)
  26. English (Cameroon)
  27. English (Ghana)
  28. English (Nigeria)
  29. English (South Africa)
  30. English (UK)
  31. English (US)
  32. Esperanto
  33. Estonian
  34. Faroese
  35. Filipino
  36. Finnish
  37. French
  38. French (Canada)
  39. French (Democratic Republic of the Congo)
  40. French (Guinea)
  41. French (Togo)
  42. Georgian
  43. German
  44. German (Austria)
  45. Greek
  46. Hebrew
  47. Hungarian
  48. Icelandic
  49. Indian
  50. Indonesian (Arab Melayu, phonetic)
  51. Indonesian (Javanese)
  52. Iraqi
  53. Irish
  54. Italian
  55. Japanese
  56. Japanese (PC-98)
  57. Kazakh
  58. Khmer (Cambodia)
  59. Korean
  60. Kyrgyz
  61. Lao
  62. Latvian
  63. Lithuanian
  64. Macedonian
  65. Malay (Jawi, Arabic Keyboard)
  66. Maltese
  67. Maori
  68. Moldavian
  69. Mongolian
  70. Montenegrin
  71. Nepali
  72. Norwegian
  73. Persian
  74. Polish
  75. Portuguese
  76. Portuguese (Brazil)
  77. Romanian
  78. Russian
  79. Serbian
  80. Sinhala (phonetic)
  81. Slovak
  82. Slovenian
  83. Spanish
  84. Spanish (Latin American)
  85. Swahili (Kenya)
  86. Swahili (Tanzania)
  87. Swedish
  88. Switzerland
  89. Taiwanese
  90. Tajik
  91. Thai
  92. Tswana
  93. Turkish
  94. Turkmen
  95. Ukrainian
  96. Urdu (Pakistan)
  97. Uzbek
  98. Vietnamese
  99. Wolof
[4mCountry of origin for the keyboard: [24m[1m