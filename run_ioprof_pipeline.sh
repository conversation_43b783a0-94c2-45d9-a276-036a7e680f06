#!/usr/bin/env bash
set -euox pipefail
BASE_DIR="/Work/blkt1d"
OUT_DIR="$BASE_DIR/ioprof_results"
LOG_DIR="$OUT_DIR/_logs"
mkdir -p "$OUT_DIR" "$LOG_DIR"
TS=$(date +%Y%m%d_%H%M%S)
RUN_LOG="$LOG_DIR/run_${TS}.log"
# tee all output to log
exec > >(tee -a "$RUN_LOG") 2>&1

echo "Installing dependencies if needed..."
if ! command -v blkparse >/dev/null 2>&1; then
  echo "ERROR: blkparse not found. Please install blktrace/blkparse first." >&2
  exit 1
fi
if ! command -v gnuplot >/dev/null 2>&1 || ! command -v pdflatex >/dev/null 2>&1; then
  export DEBIAN_FRONTEND=noninteractive
  apt-get update -y -qq
  if ! command -v gnuplot >/dev/null 2>&1; then
    apt-get install -y -qq gnuplot
  fi
  if ! command -v pdflatex >/dev/null 2>&1; then
    apt-get install -y -qq texlive-latex-extra
  fi
fi

echo "Starting processing..."
for d in "$BASE_DIR"/DERI*/blkt1d; do
  [ -d "$d" ] || continue
  name=$(basename "$(dirname "$d")")
  case_dir="$OUT_DIR/$name"
  mkdir -p "$case_dir"
  echo "Processing $name"
  # 1) 合并解析 blktrace -> blkparse.gz
  if ls "$d"/mmcblk0p7.blktrace.* >/dev/null 2>&1; then
    blkparse -i "$d/mmcblk0p7" -q -f "%d %a %S %n\n" | grep -v cfq | gzip --fast > "$case_dir/blk.out.mmcblk0p7.0.blkparse.gz"
  else
    echo "WARN: no blktrace files under $d"
  fi
  # 2) 准备 fdisk 文件（标准名）
  fdisk_src=""
  if [ -f "$d/fdisk.mmcblk0p7" ]; then
    fdisk_src="$d/fdisk.mmcblk0p7"
  else
    fdisk_src=$(ls "$d"/fdisk*mmcblk0p7* 2>/dev/null | head -n1 || true)
  fi
  if [ -n "$fdisk_src" ]; then
    cp -f "$fdisk_src" "$case_dir/fdisk.mmcblk0p7"
  else
    echo "WARN: no fdisk file under $d"
  fi
  # 3) 打包 tar 并运行 ioprof -p 生成图表/PDF
  if [ -f "$case_dir/blk.out.mmcblk0p7.0.blkparse.gz" ] && [ -f "$case_dir/fdisk.mmcblk0p7" ]; then
    tar -cf "$case_dir/mmcblk0p7.tar" -C "$case_dir" blk.out.mmcblk0p7.0.blkparse.gz fdisk.mmcblk0p7
    ( cd "$case_dir" && perl "$BASE_DIR/ioprof/ioprof.pl" -m post -t mmcblk0p7.tar -p > ioprof_report.txt 2>&1 )
  else
    echo "WARN: missing blkparse or fdisk for $name; skip analysis"
  fi
  echo "Done: $name"
  echo

done

echo "All done. Logs at $RUN_LOG"
