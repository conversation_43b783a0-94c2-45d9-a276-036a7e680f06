#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析各目录下 blktrace 相关产物，绘制：
- 写入块热力图（基于 blkparse 文本，时间-扇区二维直方图）
- dhist/qhist 直方图（基于 *_dhist.dat, *_qhist.dat）
- 写入大小方差（基于 blkparse 文本中写入长度）

注意：
- 不覆盖原始数据；若需要 blkparse 文本且不存在，则基于 blkt.bin 生成 blkparse.txt。
- 结果输出到每个数据目录下的 plots/ 与 stats/ 子目录。
"""

import os
import re
import sys
import csv
import math
import json
import errno
import pathlib
import subprocess
from typing import List, Tuple, Optional, Dict

# 仅在可用时导入，尽量减少依赖
try:
    import numpy as np
except Exception:
    np = None  # type: ignore

try:
    import matplotlib
    matplotlib.use("Agg")  # 无显示环境下绘图
    import matplotlib.pyplot as plt
except Exception:
    plt = None  # type: ignore

ROOT = pathlib.Path(os.getcwd())

BLKPARSE_TXT = "blkparse.txt"
BLKTRACE_BIN = "blkt.bin"

# 解析 blkparse 文本/日志：时间、事件字母、rwbs、起始扇区、扇区数
# 示例："179,7  0  ...  10.030579959 D WS 123456 + 8 [proc]"
RE_BLKPARSE = re.compile(r"\s*\d+,\s*\d+\s+\d+\s+\d+\s+([0-9]+\.[0-9]+)\s+(?:\d+\s+)?([A-Z])\s+([A-Za-z]+)\s+(\d+)\s*\+\s*(\d+)")
# 宽松匹配备用
RE_BLKPARSE_FALLBACK = RE_BLKPARSE


def ensure_dirs(*paths: pathlib.Path) -> None:
    for p in paths:
        p.mkdir(parents=True, exist_ok=True)


def run_blkparse_if_needed(dataset_dir: pathlib.Path, force: bool = False) -> Optional[pathlib.Path]:
    """优先使用 blkt.log（已有人类可读），否则尝试从 blkt.bin 生成 blkparse.txt。"""
    # 若已有 blkt.log，直接返回
    log = dataset_dir / "blkt.log"
    if log.exists() and log.stat().st_size > 0 and not force:
        return log
    # 否则检查或生成 blkparse.txt
    txt = dataset_dir / BLKPARSE_TXT
    if txt.exists() and not force:
        return txt
    binf = dataset_dir / BLKTRACE_BIN
    if not binf.exists():
        return None
    try:
        # 生成到临时文件再原子移动，避免部分写入
        tmp = dataset_dir / (BLKPARSE_TXT + ".tmp")
        cmd = ["blkparse", "-i", str(binf), "-o", str(tmp)]
        subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        tmp.replace(txt)
        return txt
    except FileNotFoundError:
        print("未找到 blkparse 命令，请确认已安装 blktrace 工具。", file=sys.stderr)
    except subprocess.CalledProcessError as e:
        print(f"blkparse 运行失败: {e}", file=sys.stderr)
    return None


def parse_blkparse_for_writes(txt_path: pathlib.Path) -> List[Tuple[float, int, int]]:
    """从 blkparse 文本/日志解析写入事件：(time, start_sector, nr_sectors)。"""
    writes: List[Tuple[float, int, int]] = []
    def parse_line(line: str) -> Optional[Tuple[float, int, int]]:
        if "+" not in line:
            return None
        m = RE_BLKPARSE.search(line) or RE_BLKPARSE_FALLBACK.search(line)
        if m:
            t = float(m.group(1))
            evt = m.group(2)
            rwbs = m.group(3)
            start = int(m.group(4))
            nr = int(m.group(5))
            if "C" in evt and "W" in rwbs:
                return (t, start, nr)
            return None
        # split 兜底解析
        parts = line.split()
        try:
            # 形如:
            # 0:major,minor 1:cpu 2:seq 3:time 4:pid 5:event 6:rwbs 7:sector 8:+ 9:nr ...
            # 或缺少 pid: 0:major,minor 1:cpu 2:seq 3:time 4:event 5:rwbs 6:sector 7:+ 8:nr
            # 先定位 '+'
            plus_idx = parts.index('+')
            sector = int(parts[plus_idx - 1])
            nr = int(parts[plus_idx + 1])
            # time 一般在索引3
            t = float(parts[3])
            # 判断是否写请求：rwbs 在 '+' 前 2 位或 3 位
            rwbs = parts[plus_idx - 2]
            if not any(ch in rwbs for ch in ('R','W','S','F')):
                rwbs = parts[plus_idx - 3]
            if 'W' in rwbs:
                return (t, sector, nr)
        except Exception:
            return None
        return None

    with txt_path.open("r", errors="ignore") as f:
        for line in f:
            rec = parse_line(line)
            if rec:
                writes.append(rec)
    return writes


def _set_zh_font():
    try:
        import matplotlib
        from matplotlib import font_manager
        candidates = [
            'Noto Sans CJK SC', 'Noto Sans CJK JP', 'Noto Sans CJK TC',
            'Source Han Sans CN', 'Source Han Sans',
            'WenQuanYi Zen Hei', 'WenQuanYi Micro Hei',
            'SimHei'
        ]
        available = {f.name for f in font_manager.fontManager.ttflist}
        chosen = None
        for name in candidates:
            if name in available:
                chosen = name
                break
        if chosen is None:
            for f in available:
                if ('Noto Sans CJK' in f) or ('Source Han Sans' in f) or ('WenQuanYi' in f):
                    chosen = f
                    break
        # 应用字体
        if chosen:
            matplotlib.rcParams['font.family'] = 'sans-serif'
            matplotlib.rcParams['font.sans-serif'] = [chosen, 'DejaVu Sans']
        else:
            matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans']
        matplotlib.rcParams['axes.unicode_minus'] = False
    except Exception:
        pass


def plot_write_heatmap(writes: List[Tuple[float, int, int]], out_png: pathlib.Path,
                       time_bins: Optional[int] = None, lba_bins: Optional[int] = None,
                       weighted: bool = True,
                       overlay_mbps: Optional[pathlib.Path] = None,
                       csv_out: Optional[pathlib.Path] = None) -> Optional[Dict[str, float]]:
    """绘制时间-扇区二维热力图。weighted=True 按写入长度加权。
    返回用于说明的范围信息。
    """
    if plt is None or np is None:
        print("缺少 matplotlib/numpy，无法绘制写入热力图。", file=sys.stderr)
        return None
    if not writes:
        print("未解析到写入事件，跳过热力图。", file=sys.stderr)
        return None
    _set_zh_font()

    times = np.array([w[0] for w in writes], dtype=float)
    sectors = np.array([w[1] for w in writes], dtype=float)
    # weighted=False 按写入次数计数；True 为按长度(MB)加权
    weights = None if not weighted else np.array([w[2]*0.5/1024.0 for w in writes], dtype=float)

    tmin, tmax = float(times.min()), float(times.max())
    smin, smax = float(sectors.min()), float(sectors.max())

    tb = time_bins or max(50, min(200, int(len(writes) ** 0.5)))
    sb = lba_bins or max(50, min(200, int(len(writes) ** 0.5)))

    H, xedges, yedges = np.histogram2d(times, sectors, bins=[tb, sb], weights=weights)
    # 0 为白色：将 0 掩蔽，并设置坏值为白色
    import numpy.ma as ma
    from matplotlib import colors as mcolors
    Hm = ma.masked_where(H == 0, H)
    base = plt.cm.get_cmap('viridis')
    cmap_list = base(np.linspace(0, 1, 256))
    new_cmap = mcolors.ListedColormap(cmap_list)
    new_cmap.set_bad(color='white')

    fig, ax = plt.subplots(figsize=(11, 6), dpi=150)
    im = ax.imshow(Hm.T, origin='lower', aspect='auto',
                   extent=[xedges[0], xedges[-1], yedges[0], yedges[-1]],
                   interpolation='nearest', cmap=new_cmap)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('热度 (次数 或 加权MB)')
    ax.set_xlabel('时间 (s)')
    ax.set_ylabel('起始扇区 (sector)')
    ax.set_title('写入块热力图（时间-扇区）')

    # 导出 CSV（稀疏非零，避免超大体积）
    if csv_out is None:
        csv_out = out_png.with_suffix('.csv')
    try:
        ensure_dirs(csv_out.parent)
        with csv_out.open('w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['time_left', 'time_right', 'sector_bottom', 'sector_top', 'value'])
            for i in range(len(xedges) - 1):
                for j in range(len(yedges) - 1):
                    val = float(H[i, j])
                    if val == 0.0:
                        continue
                    writer.writerow([xedges[i], xedges[i+1], yedges[j], yedges[j+1], val])
    except Exception:
        pass

    fig.tight_layout()
    fig.savefig(out_png)
    plt.close(fig)
    return {"time_min": tmin, "time_max": tmax, "sector_min": smin, "sector_max": smax}


def read_hist_dat(path: pathlib.Path) -> List[Tuple[int, int]]:
    data: List[Tuple[int, int]] = []
    if not path.exists():
        return data
    with path.open("r", errors="ignore") as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            parts = line.split()
            if len(parts) >= 2 and parts[0].isdigit():
                try:
                    bucket = int(parts[0])
                    count = int(parts[1])
                    data.append((bucket, count))
                except ValueError:
                    continue
    return data


def plot_hist(hist: List[Tuple[int, int]], title: str, xlabel: str, out_png: pathlib.Path,
              csv_out: Optional[pathlib.Path] = None) -> bool:
    if plt is None:
        print("缺少 matplotlib，无法绘制直方图。", file=sys.stderr)
        return False
    if not hist:
        print(f"{title} 无数据，跳过绘图。", file=sys.stderr)
        return False
    _set_zh_font()
    xs = [b for b, _ in hist]
    ys = [c for _, c in hist]
    fig, ax = plt.subplots(figsize=(10, 4), dpi=150)
    ax.bar(xs, ys, width=1.0, align='center', color='tab:blue', edgecolor='white', linewidth=0.3)
    # 线性坐标，设置范围与网格
    try:
        xmin, xmax = min(xs), max(xs)
        ax.set_xlim(xmin - 1, xmax + 1)
    except ValueError:
        pass
    # y 轴千分位格式与网格；x 轴使用整数自动刻度
    try:
        from matplotlib import ticker as mticker
        ax.yaxis.set_major_formatter(mticker.FuncFormatter(lambda x, pos: f"{int(x):,}" if abs(x - int(x)) < 1e-6 else f"{x}"))
        ax.xaxis.set_major_locator(mticker.MaxNLocator(nbins=10, integer=True, min_n_ticks=5))
    except Exception:
        pass
    ax.grid(axis='y', linestyle='--', alpha=0.3, linewidth=0.5)

    # 若 0~100 覆盖多数，则放大显示 0~100，并在右上角添加尾部分图
    try:
        focus_max = 100
        if xs:
            # 计算覆盖率（0~100）
            total = sum(ys)
            if total > 0:
                cover = sum(c for b, c in zip(xs, ys) if b <= focus_max) / total
                xmax = max(xs)
                if cover >= 0.7 and xmax > focus_max:
                    ax.set_xlim(-1, focus_max + 1)
                    ax.text(0.02, 0.95, f"放大 0-100（约{cover*100:.1f}%）",
                            transform=ax.transAxes, va='top', ha='left', fontsize=8,
                            bbox=dict(boxstyle='round', facecolor='white', alpha=0.6, edgecolor='lightgray'))
                    # 尝试添加 inset 显示尾部 >100
                    try:
                        from mpl_toolkits.axes_grid1.inset_locator import inset_axes
                        axins = inset_axes(ax, width="45%", height="38%", loc='upper right')
                        tail = [(b, c) for b, c in zip(xs, ys) if b > focus_max]
                        if tail:
                            tx = [b for b, _ in tail]
                            ty = [c for _, c in tail]
                            axins.bar(tx, ty, width=1.0, align='center', color='tab:blue', edgecolor='white', linewidth=0.3)
                            axins.set_title(f"> {focus_max}", fontsize=8)
                            from matplotlib import ticker as mticker
                            axins.xaxis.set_major_locator(mticker.MaxNLocator(nbins=4, integer=True, min_n_ticks=2))
                            axins.yaxis.set_major_locator(mticker.MaxNLocator(nbins=3, integer=True, min_n_ticks=2))
                            axins.grid(axis='y', linestyle='--', alpha=0.3, linewidth=0.4)
                    except Exception:
                        pass
    except Exception:
        pass

    ax.set_title(title)
    ax.set_xlabel(xlabel)
    ax.set_ylabel('计数')
    # 导出 CSV
    if csv_out is None:
        csv_out = out_png.with_suffix('.csv')
    try:
        ensure_dirs(csv_out.parent)
        with csv_out.open('w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['bucket', 'count'])
            for b, c in hist:
                writer.writerow([b, c])
    except Exception:
        pass
    fig.tight_layout()
    fig.savefig(out_png)
    plt.close(fig)
    return True


def compute_write_size_stats(writes: List[Tuple[float, int, int]]) -> Dict[str, float]:
    sizes_sectors = [nr for _, __, nr in writes]
    n = len(sizes_sectors)
    if n == 0:
        return {"count": 0, "mean_KB": 0.0, "variance_KB2": 0.0, "stddev_KB": 0.0, "min_KB": 0.0, "max_KB": 0.0}
    # 单位：KB（扇区512B）
    sizes_kb = [s * 0.5 for s in sizes_sectors]
    mean = sum(sizes_kb) / n
    var = sum((x - mean) ** 2 for x in sizes_kb) / n  # 总体方差
    std = math.sqrt(var)
    return {
        "count": float(n),
        "mean_KB": float(mean),
        "variance_KB2": float(var),
        "stddev_KB": float(std),
        "min_KB": float(min(sizes_kb)),
        "max_KB": float(max(sizes_kb)),
    }


def save_stats_csv(stats: Dict[str, float], out_csv: pathlib.Path) -> None:
    ensure_dirs(out_csv.parent)
    with out_csv.open("w", newline="") as f:
        w = csv.writer(f)
        w.writerow(["metric", "value"])
        for k, v in stats.items():
            w.writerow([k, v])


def find_datasets(root: pathlib.Path) -> List[pathlib.Path]:
    # 仅扫描第一层目录
    return [p for p in root.iterdir() if p.is_dir()]


def analyze_dataset(dataset_dir: pathlib.Path) -> Dict[str, Optional[str]]:
    print(f"分析目录: {dataset_dir}")
    plots_dir = dataset_dir / "plots"
    stats_dir = dataset_dir / "stats"
    ensure_dirs(plots_dir, stats_dir)

    # 1) 解析/生成 blkparse 文本，提取写入用于热力图与大小方差
    blkparse_txt = run_blkparse_if_needed(dataset_dir)
    write_stats: Dict[str, float] = {"count": 0.0, "mean_KB": 0.0, "variance_KB2": 0.0, "stddev_KB": 0.0, "min_KB": 0.0, "max_KB": 0.0}
    heatmap_png = None

    if blkparse_txt and blkparse_txt.exists():
        writes = parse_blkparse_for_writes(blkparse_txt)
        write_stats = compute_write_size_stats(writes)
        heatmap_png_path = plots_dir / "write_heatmap.png"
        # 热力图按写入次数加权（即计数），不叠加系统带宽
        info = plot_write_heatmap(writes, heatmap_png_path, weighted=False, overlay_mbps=None,
                                   csv_out=plots_dir / "write_heatmap.csv")
        heatmap_png = str(heatmap_png_path) if info is not None else None
        save_stats_csv(write_stats, stats_dir / "write_size_stats.csv")
        # 同时保存范围信息
        if info is not None:
            with (stats_dir / "write_heatmap_range.json").open("w") as jf:
                json.dump(info, jf, ensure_ascii=False, indent=2)
    else:
        print(f"未找到 {BLKTRACE_BIN}，无法生成写入热力图与大小方差。")

    # 2) dhist/qhist 直方图
    dhist_files = list(dataset_dir.glob("*_dhist.dat"))
    qhist_files = list(dataset_dir.glob("*_qhist.dat"))

    dhist_png = None
    qhist_png = None

    if dhist_files:
        dh = read_hist_dat(dhist_files[0])
        out = plots_dir / "dhist_histogram.png"
        if plot_hist(dh, "number of IOs", "blocks per IO", out, csv_out=plots_dir / "dhist_histogram.csv"):
            dhist_png = str(out)
    else:
        print("未找到 *_dhist.dat")

    if qhist_files:
        qh = read_hist_dat(qhist_files[0])
        out = plots_dir / "qhist_histogram.png"
        if plot_hist(qh, "Q 阶段等待分布 (qhist)", "Q 桶 (单位见 btt 文档)", out, csv_out=plots_dir / "qhist_histogram.csv"):
            qhist_png = str(out)
    else:
        print("未找到 *_qhist.dat")


    # 3) 综合图
    overview_png = None
    # if blkparse_txt and blkparse_txt.exists():
    overview_path = plots_dir / "overview.png"
    dh_for_overview = dh if (locals().get('dh') is not None) else []
    ov = plot_overview(dataset_dir, writes, dh_for_overview, overview_path) if 'writes' in locals() else None
    if ov:
        overview_png = str(overview_path)



def plot_overview(dataset_dir: pathlib.Path, writes: List[Tuple[float,int,int]], dh: List[Tuple[int,int]],
                  out_png: pathlib.Path) -> Optional[str]:
    """综合图：左-热力图(次数, 0为白)；右上-throughput；右下-dhist(线性)。并在图中加统计文本。"""
    if plt is None or np is None:
        return None
    _set_zh_font()

    # 1) 热力图（次数）
    times = np.array([w[0] for w in writes], dtype=float)
    sectors = np.array([w[1] for w in writes], dtype=float)
    if times.size == 0:
        return None
    tb = max(50, min(200, int(len(writes) ** 0.5)))
    sb = max(50, min(200, int(len(writes) ** 0.5)))
    H, xedges, yedges = np.histogram2d(times, sectors, bins=[tb, sb])
    import numpy.ma as ma
    from matplotlib import colors as mcolors
    Hm = ma.masked_where(H == 0, H)
    base = plt.cm.get_cmap('viridis')
    cmap_list = base(np.linspace(0, 1, 256))
    new_cmap = mcolors.ListedColormap(cmap_list)
    new_cmap.set_bad(color='white')

    # 2) throughput（系统或分区）
    tp_file = None
    for cand in [dataset_dir/"sys_mbps_fp.dat", dataset_dir/"179,7_mbps_fp.dat", dataset_dir/"179,0_mbps_fp.dat"]:
        if cand.exists():
            tp_file = cand
            break
    tpx, tpy = [], []
    if tp_file:
        with tp_file.open('r') as f:
            for line in f:
                ss = line.split()
                if len(ss) >= 2:
                    try:
                        tpx.append(float(ss[0]))
                        tpy.append(float(ss[1]))
                    except ValueError:
                        pass

    # 3) 布局
    fig = plt.figure(figsize=(13, 6), dpi=150)
    gs = fig.add_gridspec(2, 2, width_ratios=[2, 1])

    ax_hm = fig.add_subplot(gs[:, 0])
    im = ax_hm.imshow(Hm.T, origin='lower', aspect='auto',
                      extent=[xedges[0], xedges[-1], yedges[0], yedges[-1]],
                      interpolation='nearest', cmap=new_cmap)
    cbar = fig.colorbar(im, ax=ax_hm)
    cbar.set_label('次数')
    ax_hm.set_title('写入块热力图（次数，0为白）')
    ax_hm.set_xlabel('时间 (s)')
    ax_hm.set_ylabel('起始扇区 (sector)')

    ax_tp = fig.add_subplot(gs[0, 1])
    if tpx and tpy:
        ax_tp.plot(tpx, tpy, color='tab:orange', linewidth=1.0)
    ax_tp.set_title('Throughput (MB/s)')
    ax_tp.set_xlabel('时间 (s)')
    ax_tp.set_ylabel('MB/s')

    ax_dh = fig.add_subplot(gs[1, 1])
    if dh:
        xs = [b for b, _ in dh]
        ys = [c for _, c in dh]
        ax_dh.bar(xs, ys, width=1.0, align='center', color='tab:blue', edgecolor='white', linewidth=0.3)
        # 线性坐标范围
        try:
            xmin, xmax = min(xs), max(xs)
            ax_dh.set_xlim(xmin - 1, xmax + 1)
        except ValueError:
            pass
        # y 轴格式 & 网格；x 轴使用整数自动刻度
        try:
            from matplotlib import ticker as mticker
            ax_dh.yaxis.set_major_formatter(mticker.FuncFormatter(lambda x, pos: f"{int(x):,}" if abs(x - int(x)) < 1e-6 else f"{x}"))
            ax_dh.xaxis.set_major_locator(mticker.MaxNLocator(nbins=10, integer=True, min_n_ticks=5))
        except Exception:
            pass
        ax_dh.grid(axis='y', linestyle='--', alpha=0.3, linewidth=0.5)
        # 若 0~100 覆盖多数，则放大显示 0~100，并添加尾部 inset
        try:
            focus_max = 100
            total = sum(ys)
            if total > 0:
                cover = sum(c for b, c in zip(xs, ys) if b <= focus_max) / total
                xmax = max(xs)
                if cover >= 0.7 and xmax > focus_max:
                    ax_dh.set_xlim(-1, focus_max + 1)
                    ax_dh.text(0.02, 0.95, f"放大 0-100（约{cover*100:.1f}%）",
                               transform=ax_dh.transAxes, va='top', ha='left', fontsize=8,
                               bbox=dict(boxstyle='round', facecolor='white', alpha=0.6, edgecolor='lightgray'))
                    try:
                        from mpl_toolkits.axes_grid1.inset_locator import inset_axes
                        axins = inset_axes(ax_dh, width="45%", height="38%", loc='upper right')
                        tail = [(b, c) for b, c in zip(xs, ys) if b > focus_max]
                        if tail:
                            tx = [b for b, _ in tail]
                            ty = [c for _, c in tail]
                            axins.bar(tx, ty, width=1.0, align='center', color='tab:blue', edgecolor='white', linewidth=0.3)
                            axins.set_title(f"> {focus_max}", fontsize=8)
                            from matplotlib import ticker as mticker
                            axins.xaxis.set_major_locator(mticker.MaxNLocator(nbins=4, integer=True, min_n_ticks=2))
                            axins.yaxis.set_major_locator(mticker.MaxNLocator(nbins=3, integer=True, min_n_ticks=2))
                            axins.grid(axis='y', linestyle='--', alpha=0.3, linewidth=0.4)
                    except Exception:
                        pass
        except Exception:
            pass
    ax_dh.set_title('number of IOs (dhist)')
    ax_dh.set_xlabel('blocks per IO')
    ax_dh.set_ylabel('计数')

    # 4) 统计信息文本
    total_mb = sum(w[2] for w in writes) * 0.5 / 1024.0
    text = f"总写入量: {total_mb:.1f} MB\n写入次数: {len(writes)}\n热力 bins: {tb}x{sb}"
    ax_tp.text(0.02, 0.95, text, transform=ax_tp.transAxes, va='top', ha='left', fontsize=9,
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.7, edgecolor='lightgray'))

    fig.tight_layout()
    fig.savefig(out_png)
    plt.close(fig)
    return str(out_png)

    # 3) 综合图
    overview_png = None
    if blkparse_txt and blkparse_txt.exists():
        overview_path = plots_dir / "overview.png"
        dh_for_overview = dh if dhist_files else []
        ov = plot_overview(dataset_dir, writes, dh_for_overview, overview_path)
        if ov:
            overview_png = str(overview_path)

    return {
        "dataset": str(dataset_dir),
        "heatmap_png": heatmap_png,
        "dhist_png": dhist_png,
        "qhist_png": qhist_png,
        "overview_png": overview_png,
        "write_size_stats_csv": str(stats_dir / "write_size_stats.csv") if (stats_dir / "write_size_stats.csv").exists() else None,
    }


def main():
    datasets = find_datasets(ROOT)
    if not datasets:
        print("未发现数据目录。")
        return 1

    summary_rows: List[Dict[str, Optional[str]]] = []
    for ds in datasets:
        res = analyze_dataset(ds)
        if res:
            summary_rows.append(res)

    # 写出总览 CSV
    if summary_rows:
        summary_csv = ROOT / "blktrace_analysis_summary.csv"
        with summary_csv.open("w", newline="") as f:
            w = csv.DictWriter(f, fieldnames=list(summary_rows[0].keys()))
            w.writeheader()
            for row in summary_rows:
                w.writerow(row)
        print(f"完成。总览: {summary_csv}")
    else:
        print("未生成任何汇总数据。")
    return 0

    print(f"完成。总览: {summary_csv}")
    return 0


if __name__ == "__main__":
    sys.exit(main())

